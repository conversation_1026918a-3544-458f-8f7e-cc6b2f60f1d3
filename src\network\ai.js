import _validate from '../tool/viwor_validate'
import self from '../main'
import axios from 'axios'
import config from '../config/config' // 导入 config.js 配置
import { errors } from '../filter/error'
import wx from 'weixin-webview-jssdk'

const service = axios.create({
  baseURL: config.aiBaseURL, //import.meta.env.VITE_APP_BASE_URL,
  timeout: 2400000,
})

var isLoading = true
// var loadingInstance
service.defaults.headers.post['Content-Type'] = 'application/json'

service.interceptors.request.use(
  (config) => {
    if (isLoading) {
      // loadingInstance = Loading.service({
      //   text: "拼命加载中"
      // })
    }
    if (!_validate.checkEmpty(localStorage.getItem('setToken'))) {
      // config.headers['sq-token'] = localStorage.getItem('setToken')
      // config.headers['x-user-info'] = localStorage.getItem('setToken')
      config.headers['angela-m-token'] = localStorage.getItem('setToken')
    }
    if (!_validate.checkEmpty(sessionStorage.getItem('angela-a-token'))) {
      config.headers['angela-a-token'] = sessionStorage.getItem('angela-a-token')
    }
    config.headers['vdf-source'] = 'vdf-front'
    let req_model = {
      format: '',
      sign: '',
      source: 'vdf-front',
      timestamp: new Date().getTime() + '',
    }
    switch (config.method) {
      case 'post':
        req_model.param = config.data
        config.data = req_model
        break
      case 'get':
        break
    }
    return config
  },
  (error) => {
    if (isLoading) {
      // loadingInstance.close()
    }
    let msg = ''
    if (error.message) {
      msg = error.message
    } else {
      msg = error.msg
    }
    // Message({
    //   message: '请求异常 ' + msg,
    //   type: 'error',
    //   duration: 3 * 1000
    // })
    return Promise.reject(error)
  },
)
service.interceptors.response.use(
  (response) => {
    // if (
    //   response.headers['sq-token'] != undefined &&
    //   response.headers['sq-token'] != null &&
    //   response.headers['sq-token'] != ''
    // ) {
    //   localStorage.setItem('setToken', response.headers['sq-token'])
    // }
    // if (
    //   response.headers['set-refresh-token'] != undefined &&
    //   response.headers['set-refresh-token'] != null &&
    //   response.headers['set-refresh-token'] != ''
    // ) {
    //   localStorage.setItem('setRefreshToken', response.headers['set-refresh-token'])
    // }
    if (
      response.headers['angela-a-token'] != undefined &&
      response.headers['angela-a-token'] != null &&
      response.headers['angela-a-token'] != ''
    ) {
      sessionStorage.setItem('angela-a-token', response.headers['angela-a-token'])
    }
    const res = response.data

    if (isLoading) {
      // loadingInstance.close()
    }
    if (res.code == -2) {
      return
    }
    if (res.code < -599 && res.code > -699) {
      // self.$router.push('/login')
      wx.miniProgram.redirectTo({ url: '../login/login' })
    }
    if (res.code === 200) {
      return res
    }
    if (response.config.responseType === 'pdf') {
      return res
    }
    if (res.code !== 0) {
      let msg = ''
      if (res.message) {
        msg = res.message
      } else {
        msg = res.msg
      }
      // Message({
      //   message: exception(res.code, msg),
      //   type: 'error',
      //   duration: 3 * 1000
      // })
      return Promise.reject(res)
      // return Promise.reject({ e: 'error' })
    } else {
      return response.data
    }
  },
  (error) => {
    if (isLoading) {
      // loadingInstance.close()
    }
    let code = ''
    if (error.response) {
      code = error.response.status
    } else {
      code = '网络已断开'
    }
    // Message({
    //   message: '网络开小差(' + code + ')',
    //   type: 'error',
    //   duration: 3 * 1000
    // })
    return Promise.reject(error)
  },
)

function exception(code, message) {
  const list = errors.filter((e) => {
    return e.code === code
  })
  if (list.length === 0) {
    return message
  } else {
    return list[0].message
  }
}

export default service
