import $ from 'jquery'
export default {
  getValue: getValue,											// 【input】判断单个值是否为空，true传回 null ，false传回 去掉两端空格的值
  setValue: setValue, // 返回值判断单个值是否为空，true传回 无 ，false传回 值
  updateValue: updateValue, // 返回值判断单个值是否为空或者为无，true传回 "" ，false传回 值
  checkEmpty: checkEmpty,									// 判断单个值是否为空，是空传回 true
  checkKong: checkKong, // 判断单个值是否为空，是空传回 true 不做0验证
  checkEmptyTrim: checkEmptyTrim,					// 判断值是否为空,去掉空格是否为空 true
  checkListEmpty: checkListEmpty, 				// 判断数组值是否为空，数组的长度 true
  checkHasEmptyAttr: checkHasEmptyAttr,		// 判断数组是否有空的值	true
  checkStringSize: checkStringSize,				// 判断值的长度，传参（data，maxsize）	true
  checkPhone: checkPhone, // 判断手机号码是否正确
  checkBooleanString: checkBooleanString, // 判断boolean类型的字符串
  downloadNotFileName:downloadNotFileName,  //下载文档流的excel文件
  downloadFile:downloadFile,  //下载url的excel文件
  openTip:openTip,
  checkSearch: checkSearch,  // 校验检索条件是否为空
  stime:stime  // 将2020-02-20 12:14:14  转换为 02-20 12:14
}
/** 提示信息
 * @param vm
 * @param message { String } -必选 提示内容
 * @param type { String } -可选 提示信息的类型 失败：error 警告：warning 成功：success 默认为消息
 */
function openTip(vm, msg, type) {
  vm.$message({
    message: msg,
    showClose: true, // { Boolean  } -可选 提示信息是否可手动关闭
    type: type
  })
}
function downloadFile(data, filename){
  let link = document.createElement('a')
  link.style.display = 'none'
  link.href = data
  link.setAttribute('download', filename)
  document.body.appendChild(link)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}
function downloadNotFileName(data, filename) {
  const url = URL.createObjectURL(new Blob([data.data]))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', filename)
  document.body.appendChild(link)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}
function checkBooleanString(val) {
  if (val === 'true' || val === true || val === 1) {
    return true
  } else {
    return false
  }
}
/*  判断值是否为空，空：传回null  */
function getValue(val) {
  if (val === undefined || val === null || (val === '' && val !== 0)) {
    return null
  }
  return $.trim(val)
}
/*  判断单个值是否为空，true传回 无  */
function setValue(val) {
  if (val === undefined || val === null || (val === '' && val !== 0)) {
    return '无'
  }
  return val
}
/*  判断单个值是否为空或者为无，true传回 ''  */
function updateValue(val) {
  if (val === undefined || val === null || (val === '' && val !== 0) || val === '无') {
    return ''
  }
  return val
}
/*  判断值是否为空  */
function checkEmpty(val) {
  if (val === undefined || val === null || (val === '' && val !== 0)) {
    return true
  }
  return false
}
/*  判断值是否为空  */
function checkKong(val) {
  if (val === undefined || val === null || val === '') {
    return true
  }
  return false
}
/*  判断值是否为空  */
function checkEmptyTrim(val) {
  if (val === undefined || val === null || (val === '' && val !== 0)) {
    return true
  }
  val = $.trim(val)
  if (val === null || (val === '' && val !== 0)) {
    return true
  }
  return false
}
/*  判断数组的长度  */
function checkListEmpty(val) {
  if (val === undefined || val === null) {
    return true
  }
  if (val.length < 1) {
    return true
  }
  return false
}
/*  判断数组是否有空的值  */
function checkHasEmptyAttr(val) {
  for (const i in val) {
    if (val[i] === undefined || val[i] === null || val[i] === '') {
      return true
    }
  }
  return false
}
/*  判断值的长度  */
function checkStringSize(val, maxSize) {
  if (val.length > maxSize) {
    return true
  }
  return false
}
/*  校验手机号码格式  */
function checkPhone(val) {
  // var reg = /^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|(147))\\d{8}$/
  var reg = /^1[34578]\d{9}$/
  if (reg.test(val)) {
    return false
  } else {
    return true
  }
}
function exception(code, message) {
  const list = errors.filter(e => {
    return e.code === code
  })
  if (list.length === 0) {
    return message
  } else {
    return list[0].message
  }
}
/*  校验检索条件是否为空  */
function checkSearch(currObj) {
  for (let val in currObj) {
    if (currObj[val] !== "" && currObj[val] !== undefined && currObj[val] !== null && currObj[val] !== NaN) {
      return false
    }
  }
  return true
}
function stime(inputTime) {
  let time = inputTime.replace(/-/g, "/")
  let datetime = new Date(time)
  let Ytime = datetime.getFullYear() + '-';
  let Mtime = (datetime.getMonth() + 1 < 10 ? '0' + (datetime.getMonth() + 1) : datetime.getMonth() + 1) + '-';
  let Dtime = (datetime.getDate() < 10 ? '0' + (datetime.getDate()) : datetime.getDate());
  let htime = (datetime.getHours() < 10 ? '0' + (datetime.getHours()) : datetime.getHours()) + ':';
  let mtime = (datetime.getMinutes() < 10 ? '0' + (datetime.getMinutes()) : datetime.getMinutes());
  let stime = (datetime.getSeconds() < 10 ? '0' + (datetime.getSeconds()) : datetime.getSeconds());
  let newtime = Mtime + Dtime + " " + htime + mtime
  return newtime;
}