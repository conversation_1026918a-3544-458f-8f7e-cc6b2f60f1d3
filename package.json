{"name": "angela-mini", "version": "********", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "build:test": "vite build --mode test", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.2.4", "axios": "^1.8.4", "echarts": "^5.6.0", "element-plus": "^2.9.7", "highlight.js": "^11.11.1", "jquery": "^3.7.1", "markdown-it": "^14.1.0", "pinia": "^3.0.1", "vant": "^4.9.18", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0", "weixin-webview-jssdk": "^0.0.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "less": "^4.2.2", "less-loader": "^12.2.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "3.5.3", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}