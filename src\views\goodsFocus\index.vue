<script setup>
// http://sytdemo-sq.sapi.viwor.net/opsLogin?userName=80100000&opsToken=97df047569b857095a4fea5754126bc1
import defaultImg from '@/assets/images/goodsFocus/default.png'
import { h, ref, reactive, onBeforeMount, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
import { Search } from '@element-plus/icons-vue'
import { sortGroup, getGoodsList } from '@/api/goodsFocus'
import { showToast, showConfirmDialog, showFailToast } from 'vant'
import draggable from 'vuedraggable'
import useClipboard from 'vue-clipboard3'

import {
  selectUserGoodsGroupList,
  selectGoodFocusIndex,
  addGroup,
  updateGroup,
  deleteGroup,
  sortGroupGoods,
  deleteGroupGoods,
  shareGroup,
  deleteShareGroup,
} from '@/api/goodsFocus'
// import { fa } from 'element-plus/es/locale'
import wx from 'weixin-webview-jssdk'
import { api } from '@/api/ai'

const { toClipboard } = useClipboard()
const showBottom = ref(false)
const loginStatus = ref(true)
const showCopyBarcode = ref(false)

// 获取当前日期区间(yy-mm-dd - yy-mm-dd)
const getCurrentDate = () => {
  const date = new Date()
  const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
  return `${dateStr} - ${dateStr}`
}

// 获取昨天日期区间(yy-mm-dd - yy-mm-dd)
const getYesterdayDate = () => {
  const date = new Date()
  date.setDate(date.getDate() - 1)
  const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
  return `${dateStr} - ${dateStr}`
}

// 获取本周日期区间(周一 - 周日)(yy-mm-dd - yy-mm-dd)
const getWeekStartDate = () => {
  const date = new Date()
  const day = date.getDay()
  const diff = date.getDate() - day + (day === 0 ? -6 : 1)
  date.setDate(diff)
  const startDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`

  const endDate = new Date(date)
  endDate.setDate(date.getDate() + 6)
  const endDateStr = `${endDate.getFullYear()}-${padZero(endDate.getMonth() + 1)}-${padZero(endDate.getDate())}`

  return `${startDate} - ${endDateStr}`
}

// 获取本月日期区间(1号 - 月末)(yy-mm-dd - yy-mm-dd)
const getMonthStartDate = () => {
  const date = new Date()
  const startDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-01`

  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  const endDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(lastDay)}`

  return `${startDate} - ${endDate}`
}

// 补零函数
const padZero = (num) => {
  return num < 10 ? `0${num}` : num
}

const currentTimeIndex = ref(0)

const timeList = reactive([
  {
    value: getCurrentDate(), // 实时
    label: '实时',
  },
  {
    value: getYesterdayDate(), // 昨天
    label: '昨天',
  },
  {
    value: getWeekStartDate(), // 本周
    label: '本周',
  },
  {
    value: getMonthStartDate(), // 本月
    label: '本月',
  },
  {
    value: '',
    label: '开始时间',
  },
  {
    value: '',
    label: '结束时间',
  },
])

const timeChange = (item, index) => {
  currentTimeIndex.value = index

  if (currentTimeIndex.value !== 4 && currentTimeIndex.value !== 5) {
    currentDate.value.length = 0
    timeValue.value = timeList[currentTimeIndex.value].value
    startTime.value = timeValue.value.split(' - ')[0]
    endTime.value = timeValue.value.split(' - ')[1]
  } else {
    showDateBottom.value = true
    if (currentTimeIndex.value === 4) {
      showDateTyepe.value = 'startTime'
      startTime.value = ''
    }
    if (currentTimeIndex.value === 5) {
      showDateTyepe.value = 'endTime'
      endTime.value = ''
    }
  }
  // if (item === 'startTime') {
  //   showDateBottom.value = true
  //   showDateTyepe.value = item
  // } else if (item === 'endTime') {
  //   showDateBottom.value = true
  //   showDateTyepe.value = item
  // }
}

const startTime = ref('')

const endTime = ref('')

const showDateBottom = ref(false)

const showDateTyepe = ref('')

const currentDate = ref([])

const minDate = ref(new Date(new Date().getFullYear() - 2, 0, 1))

const currentGroupId = ref('')

const scanCodeResult = ref('')

const columnsType = ['year', 'month', 'day']
const formatter = (type, option) => {
  if (type === 'year') {
    option.text += '年'
  }
  if (type === 'month') {
    option.text += '月'
  }
  if (type === 'day') {
    option.text += '日'
  }
  return option
}

const confirmDate = () => {
  // 新增校验逻辑
  if (showDateTyepe.value === 'startTime') {
    startTime.value = currentDate.value.join('-')
  } else if (showDateTyepe.value === 'endTime') {
    endTime.value = currentDate.value.join('-')
  }

  if (startTime.value && endTime.value) {
    const start = new Date(startTime.value)
    const end = new Date(endTime.value)
    if (start > end) {
      showToast('开始时间不能大于结束时间')
      return
    }
  }
  if (showDateTyepe.value === 'startTime') {
    timeList[currentTimeIndex.value].value = startTime.value
    timeList[currentTimeIndex.value].label = startTime.value
  }
  if (showDateTyepe.value === 'endTime') {
    timeList[currentTimeIndex.value].value = endTime.value
    timeList[currentTimeIndex.value].label = endTime.value
  }
  timeValue.value = startTime.value + ' - ' + endTime.value
  showDateBottom.value = false
}

const sureTiem = () => {
  if (currentTimeIndex.value !== 4 && currentTimeIndex.value !== 5) {
    timeValue.value = timeList[currentTimeIndex.value].value
    showBottom.value = false
    loadIndexData()
  } else {
    if (currentTimeIndex.value === 4 || currentTimeIndex.value === 5) {
      if (!timeList[4].value || !timeList[5].value) {
        showToast('您有自定义时间未选择')
        return
      }
    }
    if (startTime.value && endTime.value) {
      timeValue.value = `${startTime.value} - ${endTime.value}`
      showBottom.value = false
      loadIndexData()
    }
  }
}

const timeValue = ref(timeList[0].value)

const handleTimeValue = (dateRange) => {
  return dateRange
    .split(' - ')
    .map((date) => date.slice(5)) // 取出 MM-DD 部分
    .join(' - ')
}

const searchQuery = ref('')

const tabsList = ref([])

const activeTabs = ref(0)

const isShareStatus = ref(0)

const isStatus = ref(0)

const handleTabsClick = (item, index) => {
  // isShareStatus.value = item.isShare
  isStatus.value = item.status
  activeTabs.value = index
  localStorage.setItem('activeTabs', index)
  currentGroupId.value = item.groupId
  localStorage.setItem('currentGroupId', item.groupId)
  loadIndexData()
}

const indexData = ref({})

const cruIndexMap = ref({})

const goodsList = ref([])

// 加载首页数据
const loadIndexData = async () => {
  selectGoodFocusIndex({
    appVer: '2.4.8',
    groupId: currentGroupId.value,
    selectDate: startTime.value,
    selectEndDate: endTime.value,
  })
    .then((res) => {
      console.log(res, 'res-selectGoodFocusIndex')
      indexData.value = res
      cruIndexMap.value = res.cruIndexMap
      goodsList.value = res.focusCruIndexList
    })
    .catch((err) => {
      console.log(err, 'err-selectGoodFocusIndex')
    })
}
// 加载商品关注组
const loadFocusGroup = (goodsId) => {
  selectUserGoodsGroupList(goodsId ? goodsId : '')
    .then((res) => {
      tabsList.value = res

      console.log(res, 'res-selectUserGoodsGroupList')
      if (res && res.length > 0 && (currentGroupId.value === '' || currentGroupId.value == null)) {
        currentGroupId.value = tabsList.value[0].groupId
      }

      // isShareStatus.value = tabsList.value[activeTabs.value].isShare
      isStatus.value = tabsList.value[activeTabs.value].status

      loadIndexData()
    })
    .catch((err) => {
      console.log(err, 'err-selectUserGoodsGroupList')
    })
}
// 加载关注组指标
const loadGroupMetrics = () => {}
// 加载关注组内商品
const loadGroupGoods = () => {}

// 抄送分享
const shareUser = () => {
  console.log('route to shareUser========')
  router.push({
    path: '/goodsFocus/shareUser',
    query: {
      g: currentGroupId.value,
    },
  })
}

//============ 分组管理 ========================
const showManagerGroup = ref(false)
const showManagerGoods = ref(false)
const groupManagerDialogVisible = ref(false)
const groupTitle = ref('')
const groupEditDialogTitle = ref('')
const editGroupId = ref('')

const handleManagerGroup = () => {
  showManagerGroup.value = true
}
const groups = reactive([
  // { name: '我的关注', id: 0 },
  // { name: '七夕单品', id: 1 },
  // { name: '活动促销', id: 2 },
  // { name: '中秋礼盒', id: 3 },
])

//拖拽结束的事件
const onEndSortGroup = async () => {
  const ids = tabsList.value.map((item) => item.groupId)
  await sortGroup(ids)
  activeTabs.value = 0
  currentGroupId.value = ids[0]
}

// 添加分组
const handleAddGroup = () => {
  // 检查是否已有20个分组，最多可添加20个分组
  if (groups.length >= 20) {
    showToast('最多可添加20个商品组合')
    return
  }
  groupTitle.value = ''
  groupEditDialogTitle.value = '添加组合'
  groupManagerDialogVisible.value = true
}

// 编辑分组
const handleEditGroup = (data) => {
  groupTitle.value = data.groupName
  editGroupId.value = data.groupId
  groupEditDialogTitle.value = '编辑组合'
  groupManagerDialogVisible.value = true
}

// 删除分组
const handleDeleteGroup = (data) => {
  showConfirmDialog({
    title: '删除分组',
    message: () =>
      h('span', [
        '您确定要删除',
        h('span', { style: { color: 'red', fontWeight: 'bold' } }, data.groupName),
        '分组吗？',
      ]),
  })
    .then(async () => {
      // on confirm
      if (data.isShare == 0) {
        await deleteGroup(data.groupId)
      } else {
        await deleteShareGroup(data.groupId)
      }
      const index = tabsList.value.findIndex((group) => group.groupId === data.groupId)
      if (index !== -1) {
        tabsList.value.splice(index, 1)
      }
      // const index = groups.findIndex((group) => group.groupId === data.groupId)
      // if (index !== -1) {
      //   groups.splice(index, 1)
      // }
    })
    .catch(() => {
      // on cancel do nothing
    })
}

//表单校验阻止关闭弹窗
const beforeClose = async (action) => {
  if (action === 'confirm') {
    try {
      if (groupTitle.value === '' || groupTitle.value.length == 0) {
        showToast('组合名称不能为空')
        return false
      }
      if (groupTitle.value.length > 10) {
        showToast('组合请不要超过10个字符')
        return false
      }
      if (groupEditDialogTitle.value === '添加组合') {
        const id = await addGroup({ name: groupTitle.value })
        groups.push({ name: groupTitle.value, id: id })
        selectUserGoodsGroupList('')
          .then((res) => {
            tabsList.value = res

            console.log(res, 'res-selectUserGoodsGroupList')
            if (res && res.length > 0) {
              currentGroupId.value = tabsList.value[0].groupId
            }

            loadIndexData()
          })
          .catch((err) => {
            console.log(err, 'err-selectUserGoodsGroupList')
          })
        // return
        return true
      } else if (groupEditDialogTitle.value === '编辑组合') {
        const res = await updateGroup({
          description: groupTitle.value,
          groupId: editGroupId.value,
          isDefault: '',
          name: groupTitle.value,
        })
        // 修改标题

        tabsList.value.map((item) => {
          if (item.groupId == editGroupId.value) {
            item.groupName = groupTitle.value
          }
        })
        // groups.map((item) => {
        //   console.log(item, 'item')
        //   if (item.groupId == editGroupId.value) {
        //     item.name = groupTitle.value
        //   }
        // })
        return true
      }
    } catch (error) {
      console.log(error)
      const msg = groupEditDialogTitle.value == '添加组合' ? '组合添加失败' : '组合编辑失败'
      showToast(msg)
      return true
    }
  } else {
    return true
  }
}
//============ 分组管理 ========================

//============ 商品管理 ========================
const checkedGoodsCnt = ref(0)
const groupChecked = ref(false)

// 编辑商品
const handleManagerGoods = () => {
  showManagerGoods.value = true
}

// 全选
const checkAll = () => {
  goodsList.value.forEach((goods) => {
    goods.selected = groupChecked.value
  })
  countCheckedGoods()
}

// 选中商品
const handleSelectGoods = (goods) => {
  goods.selected = !goods.selected
  countCheckedGoods()
}

// 统计选中商品数量
const countCheckedGoods = () => {
  checkedGoodsCnt.value = 0
  goodsList.value.forEach((goods) => {
    if (goods.selected) {
      checkedGoodsCnt.value++
    }
  })
  if (groupChecked.value == true && checkedGoodsCnt.value != goodsList.value.length) {
    groupChecked.value = false
  }
}

// 删除商品
const handleDeleteGoods = () => {
  if (checkedGoodsCnt.value < 1) return
  showConfirmDialog({
    title: '删除分组内商品',
    message: '您确定要删除吗？',
  })
    .then(async () => {
      // on confirm
      const goodsIds = goodsList.value.filter((item) => item.selected).map((item) => item.GoodsID)
      const res = await deleteGroupGoods({
        goodsIds: goodsIds,
        groupId: currentGroupId.value,
      })
      goodsList.value.map((item, index) => {
        if (item.selected) {
          goodsList.value.splice(index, 1)
        }
      })
    })
    .catch(() => {
      // on cancel do nothing
    })
}

// 排序商品
const onEndSortGoods = async () => {
  const focusIds = goodsList.value.map((goods) => goods.focusId)
  await sortGroupGoods(focusIds)
}

// 置顶
const handleMoveTop = async (index) => {
  if (index === 0) return // 已在顶部则忽略

  const item = goodsList.value[index]
  goodsList.value.splice(index, 1) // 移除原位置的元素
  goodsList.value.unshift(item) // 添加到数组开头

  // 更新排序
  await sortGroupGoods(goodsList.value.map((goods) => goods.focusId))
}

// 进入商品详情
const handleGoodsClick = (goodsId) => {
  router.push({
    path: '/goodsInfo',
    query: {
      t: sessionStorage.getItem('token'),
      u: sessionStorage.getItem('userId'),
      g: goodsId,
    },
  })
}

//============ 商品管理 ========================
//============ 商品搜索 ========================
const onSearch = () => {
  router.push({
    path: '/goodsFocus/searchGoods',
    query: {
      t: sessionStorage.getItem('token'),
      u: sessionStorage.getItem('userId'),
      g: currentGroupId.value,
    },
  })
}
//============ 商品搜索 ========================

const getTodayString = () => {
  const date = new Date()
  const dateStr = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
  return dateStr
}

const handleSearchIndustryData = () => {
  router.push({
    path: '/goodsFocus/searchGoods',
    query: {
      t: sessionStorage.getItem('token'),
      u: sessionStorage.getItem('userId'),
      b: scanCodeResult.value,
    },
  })
}

const copyToClipboard = async () => {
  try {
    await toClipboard(scanCodeResult.value)
    showToast('复制成功')
  } catch (e) {
    console.error(e)
    showToast('复制失败')
  }
}

// 扫码
const scanCode = () => {
  wx.scanQRCode({
    needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
    scanType: ['barCode'], // 可以指定扫二维码还是一维码，默认二者都有['qrCode', 'barCode']
    success: function (res) {
      console.log(res, 'res-scanQRCode')
      if (res.resultStr === 'scan resultStr is here') {
        showFailToast('未能扫描到条形码')
        return
      }
      // 当needResult 为 1 时，扫码返回的结果
      scanCodeResult.value = res.resultStr
      scanCodeResult.value = scanCodeResult.value.replace('EAN_13,', '').replace('CODE_128,', '')
      if (scanCodeResult.value.includes(',')) {
        scanCodeResult.value = scanCodeResult.value.split(',')[1]
      }
      // 查询企业数据
      getGoodsList({
        type: '1',
        goods: scanCodeResult.value,
      }).then((res) => {
        console.log(res, 'res-getGoodsList')
        if (res != null && res.length > 0) {
          res.forEach((item) => {
            if (item.goodsId !== '') {
              handleGoodsClick(item.goodsId)
            }
          })
        } else {
          // 没查到企业内数据，显示提示框
          showCopyBarcode.value = true
        }
      })
    },
    fail: function (res) {
      console.log(res, 'res-scanQRCode-fail')
      showFailToast('未能扫描到条形码')
    },
  })
}

const nexoraAuthLogin = () => {
  api
    .nexoraAuthLogin()
    .then(async (res) => {
      if (res.code * 1 === 0) {
        sessionStorage.setItem('userInfo', JSON.stringify(res.result))
        wxConfig()
      } else {
        showToast({
          type: 'fail',
          message: 'res.message',
          duration: 3000,
        })
        loginStatus.value = false
        wx.miniProgram.redirectTo({ url: '../login/login' })
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

const wxConfig = () => {
  api
    .getWxJsapiSignature(location.href.split('#')[0])
    .then((res) => {
      const result = res.result
      console.log(JSON.stringify(res))
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: result.appId, // 必填，公众号的唯一标识
        timestamp: result.timestamp, // 必填，生成签名的时间戳
        nonceStr: result.nonceStr, // 必填，生成签名的随机串
        signature: result.signature, // 必填，签名
        jsApiList: ['scanQRCode'], // 必填，需要使用的JS接口列表
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

router.beforeEach((to, from) => {
  console.log(to.path)
  console.log(from.path)
  localStorage.setItem('forgetGroupIndex', 1)
})

onBeforeMount(() => {
  // 组件挂载前执行，此时模板已编译但尚未挂载到DOM
  document.title = '商品关注'

  if (JSON.stringify(route.query) !== '{}') {
    if (typeof route.query.t !== 'undefined' && typeof route.query.u !== 'undefined') {
      sessionStorage.setItem('token', route.query.t)
      localStorage.setItem('setToken', route.query.t)
      sessionStorage.setItem('userId', route.query.u)
    }
  }
})

onMounted(() => {
  nexoraAuthLogin()
  startTime.value = getTodayString()
  endTime.value = getTodayString()
  console.log(localStorage.getItem('forgetGroupIndex'), 'forgetGroupIndex')
  if (localStorage.getItem('forgetGroupIndex') * 1 === 1) {
    activeTabs.value = localStorage.getItem('activeTabs') * 1
    console.log(activeTabs.value, 'activeTabs.value')
    currentGroupId.value = localStorage.getItem('currentGroupId')
    console.log(currentGroupId.value, 'currentGroupId.value')
    localStorage.setItem('forgetGroupIndex', 0)
  }
  loadFocusGroup('')
  loadGroupMetrics()
  loadGroupGoods()
})
</script>

<template>
  <div class="product-follow">
    <!-- 日期选择器和搜索框 -->
    <div class="top-filter">
      <div class="time-box" @click="showBottom = !showBottom">
        <img class="top-filter-date-icon" src="../../assets/images/goodsFocus/date.png" />
        <div class="top-filter-date-text">{{ handleTimeValue(timeValue) }}</div>
      </div>
      <div class="top-filter-input">
        <el-input
          v-model="searchQuery"
          placeholder="搜索商品名称/条码"
          :prefix-icon="Search"
          @focus="onSearch"
        />
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="tabs-box">
      <div class="tabs-box-item-group">
        <div
          v-for="(item, index) in tabsList"
          :key="index"
          class="tabs-box-items"
          :class="[activeTabs === index ? 'tabs-box-items-activation' : '']"
          @click="handleTabsClick(item, index)"
        >
          <img
            class="icon"
            src="../../assets/images/goodsFocus/share.png"
            v-if="item.isShare * 1 === 1 && activeTabs !== index"
          />
          <img
            src="../../assets/images/goodsFocus/isShare.png"
            alt="group"
            v-if="item.isShare * 1 === 1 && activeTabs === index"
          />
          {{ item.groupName }}
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview">
      <div class="overview-stats">
        <div class="grid-content">
          <p class="title">
            <img
              src="../../assets/images/goodsFocus/saleValue.png"
              alt=""
              class="saleValue-img"
            />销售额
          </p>
          <h2>{{ (Number(cruIndexMap.saleValue) || 0).toFixed(2) }}</h2>
          <p class="year-on-year">
            同比：{{ (Number(cruIndexMap.saleValueRate) * 100 || 0) > 0 ? '+' : '-'
            }}{{ (Number(cruIndexMap.saleValueRate) * 100 || 0).toFixed(2) }}%
          </p>
          <p class="monopoly">
            环比：{{ (Number(cruIndexMap.saleVGrRate) * 100 || 0) > 0 ? '+' : '-'
            }}{{ (Number(cruIndexMap.saleVGrRate) * 100 || 0).toFixed(2) }}%
          </p>
        </div>
        <div class="grid-content">
          <p class="title">
            <img src="../../assets/images/goodsFocus/saleQty.png" alt="" class="saleQty-img" />销量
          </p>
          <h2>{{ (Number(cruIndexMap.saleQty) || 0).toFixed(2) }}</h2>
          <p class="year-on-year">
            同比：{{ (Number(cruIndexMap.saleQtyRate) * 100 || 0) > 0 ? '+' : '-'
            }}{{ (Number(cruIndexMap.saleQtyRate) * 100 || 0).toFixed(2) }}%
          </p>

          <p class="monopoly">
            环比：{{ (Number(cruIndexMap.saleQGrRate) * 100 || 0) > 0 ? '+' : '-'
            }}{{ (Number(cruIndexMap.saleQGrRate) * 100 || 0).toFixed(2) }}%
          </p>
        </div>
        <div class="grid-content">
          <p class="title">
            <img
              src="../../assets/images/goodsFocus/stockQty.png"
              alt=""
              class="stockQty-img"
            />库存量
          </p>
          <h2>{{ (Number(cruIndexMap.stockQty) || 0).toFixed(2) }}</h2>
          <p class="year-on-year">
            周转：{{ (Number(cruIndexMap.stockDay) * 100 || 0).toFixed(2) }}天
          </p>
          <p class="monopoly">
            环比： ¥{{ (Number(cruIndexMap.stockDayGrRate) * 100 || 0).toFixed(2) }}
          </p>
        </div>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="product-list">
      <div class="product-list-header">
        <div class="left">
          <div class="left-item-text">
            <span>共{{ goodsList.length }}件商品</span>
          </div>
          <div @click="handleManagerGoods" class="left-item" v-if="isStatus * 1 === 0">
            <img class="icon" src="../../assets/images/goodsFocus/edit.png" />
            <span>编辑</span>
          </div>
          <div @click="shareUser" class="left-item" v-if="isStatus * 1 === 0">
            <img class="icon" src="../../assets/images/goodsFocus/share.png" />
            <span>分享</span>
          </div>
        </div>
        <div class="right">
          <div class="right-item" @click="handleManagerGroup()">分组管理</div>
        </div>
      </div>

      <div
        v-for="goods in goodsList"
        :key="goods.GoodsID"
        class="product-item"
        @click="handleGoodsClick(goods.GoodsID)"
      >
        <div class="product-item-header">
          <div class="product-image">
            <img :src="goods.GoodsImg || defaultImg" alt="Product Image" style="width: 100%" />
          </div>
          <div class="product-details">
            <h4>
              <span class="tag">
                <van-tag
                  :type="
                    {
                      1: 'danger',
                      2: 'warning',
                      3: 'success',
                    }[goods.IndexFlag]
                  "
                >
                  {{ { 1: 'A', 2: 'B', 3: 'C' }[goods.IndexFlag] }}
                </van-tag>
              </span>
              <span class="name">{{ goods.GoodsName }}</span>
            </h4>
            <div class="product-info">
              <p>编码：{{ goods.GoodsID }}</p>
              <p>条码：{{ goods.BarCode || '' }}</p>
            </div>
          </div>
        </div>
        <div class="product-item-bottom">
          <div class="product-item-bottom-item">
            <small>销售额</small>
            <span>{{ goods.SaleValue || 0.0 }}</span>
          </div>
          <div class="product-item-bottom-item">
            <small>销量</small>
            <span>{{ goods.SaleQty || 0.0 }}</span>
          </div>
          <div class="product-item-bottom-item">
            <small>库存量</small>
            <span>{{ goods.StockQty || 0.0 }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-box">
      <div class="footer-box-item" @click="scanCode">
        <img src="../../assets/images/goodsFocus/scanCode.png" alt="" />
        <span>扫一扫</span>
      </div>
    </div>
    <van-popup
      v-model:show="showBottom"
      position="bottom"
      :style="{ height: '25%' }"
      :close-on-click-overlay="false"
      round
    >
      <div class="time-box-title">
        <span @click="showBottom = false">取消</span> <span>选择时间</span>
        <span @click="sureTiem">确定</span>
      </div>
      <div class="time-box-list">
        <van-button
          :type="
            currentTimeIndex === 4 || currentTimeIndex === 5
              ? index === 4 || index === 5
                ? 'primary'
                : 'default'
              : index === currentTimeIndex
                ? 'primary'
                : 'default'
          "
          v-for="(item, index) in timeList"
          :key="index"
          size="small"
          @click="timeChange(item, index)"
        >
          <!-- <img
            src="../../assets/images/goodsFocus/select-date.png"
            alt=""
            v-if="index === 4 || index === 5"
          /> -->
          {{ item.label }}</van-button
        >
      </div>

      <!-- <div class="customize-tiem-box">
        <el-input
          v-model="startTime"
          placeholder="开始时间"
          readonly
          @click="timeChange('startTime', 4)"
        />
        至
        <el-input
          v-model="endTime"
          placeholder="结束时间"
          readonly
          @click="timeChange('endTime', 4)"
        />
      </div> -->
      <!-- <div class="customize-tiem-button">
        <van-button type="default" @click="showBottom = false">取消 </van-button>
        <van-button type="primary" @click="sureTiem"> 确定</van-button>
      </div> -->
    </van-popup>
    <van-popup
      v-model:show="showDateBottom"
      position="bottom"
      :style="{ height: '40%' }"
      :close-on-click-overlay="false"
      ><van-date-picker
        v-model="currentDate"
        title="选择年月日"
        :columns-type="columnsType"
        :formatter="formatter"
        :min-date="minDate"
        @cancel="showDateBottom = false"
        @confirm="confirmDate"
    /></van-popup>

    <!-- 分组管理右侧抽屉 -->
    <van-popup
      v-model:show="showManagerGroup"
      position="right"
      :style="{ width: '75%', height: '100%' }"
    >
      <div class="group-manager-title-box">
        分组管理<van-icon name="cross" @click="showManagerGroup = !showManagerGroup" />
      </div>
      <div class="group-manager-tip">最多可添加20个组合，长按可拖动排序</div>
      <draggable
        :list="tabsList"
        class="group-manager-item-wrap"
        ghost-class="ghost"
        chosen-class="chosenClass"
        animation="300"
        @end="onEndSortGroup"
        item-key="groupId"
      >
        <template #item="{ element }">
          <div class="item">
            <div class="item-text">
              {{ element.groupName }}
              <img
                src="../../assets/images/goodsFocus/group-manager-isShare.png"
                v-if="element.isShare * 1 === 1"
              />
            </div>
            <div class="item-btn">
              <img
                src="../../assets/images/goodsFocus/group-manager-edit.png"
                alt=""
                @click="handleEditGroup(element)"
                v-if="element.status * 1 === 0"
              />
              <img
                src="../../assets/images/goodsFocus/group-manager-delete.png"
                alt=""
                @click="handleDeleteGroup(element)"
              />
              <!-- <van-button
                icon="edit"
                type="primary"
                size="small"
                plain
                hairline
                @click="handleEditGroup(element)"
                >编辑</van-button
              > -->
              <!-- <van-button
                icon="delete"
                type="danger"
                size="small"
                plain
                hairline
                @click="handleDeleteGroup(element)"
                >删除</van-button
              > -->
            </div>
          </div>
        </template>
      </draggable>

      <van-button type="primary" class="group-manager-top-btn" block @click="handleAddGroup"
        >新增分组</van-button
      >
    </van-popup>

    <van-dialog
      v-model:show="groupManagerDialogVisible"
      :title="groupEditDialogTitle"
      show-cancel-button
      :before-close="beforeClose"
    >
      <van-field
        v-model="groupTitle"
        placeholder="请输入分组名称，最多六个中文"
        name="title"
        maxlength="6"
      />
    </van-dialog>
    <!-- 分组管理右侧抽屉 -->

    <!-- 编辑商品 -->
    <van-popup
      v-model:show="showManagerGoods"
      position="right"
      :style="{ width: '75%', height: '100%' }"
    >
      <div class="goods-manager">
        <div class="goods-manager-title-box">
          商品管理<van-icon name="cross" @click="showManagerGoods = !showManagerGoods" />
        </div>
        <div class="goods-manager-top">
          <div class="goods-manager-top-radio">
            <van-field name="check" @click="checkAll">
              <template #input>
                <van-checkbox v-model="groupChecked" shape="square" /> 全选
              </template>
            </van-field>
            <!-- <div class="goods-manager-top-info">
              已选择 <b>{{ checkedGoodsCnt }}</b> 个
            </div> -->
          </div>
          <div class="goods-manager-btn">
            <span @click="handleDeleteGoods">删除</span>
            <!-- <van-button plain type="danger" size="small" @click="handleDeleteGoods"
              >删除</van-button
            > -->
          </div>
        </div>
        <div class="goods-manager-list">
          <draggable
            :list="goodsList"
            ghost-class="ghost"
            chosen-class="chosenClass"
            animation="300"
            @end="onEndSortGoods"
            item-key="focusId"
          >
            <template #item="{ element, index }">
              <div class="item" :class="[element.selected ? 'item-select' : '']">
                <div class="checkbox-wrapper" @click.stop="handleSelectGoods(element)">
                  <van-checkbox :model-value="element.selected" shape="square" />
                </div>
                <div class="goods-manager-right-box">
                  <div class="item-image">
                    <img :src="element?.GoodsImg || defaultImg" alt="Product Image" />
                  </div>
                  <div class="item-details">
                    <h4>
                      <span class="tag">
                        <van-tag
                          :type="
                            {
                              1: 'danger',
                              2: 'warning',
                              3: 'success',
                            }[element.IndexFlag]
                          "
                        >
                          {{ { 1: 'A', 2: 'B', 3: 'C' }[element.IndexFlag] }}
                        </van-tag>
                      </span>
                      <span class="name">{{ element.GoodsName }}</span>
                    </h4>
                    <div class="item-info">
                      <p>编码：{{ element.GoodsID || '' }}</p>
                      <div class="drag"><img src="../../assets/images/goodsFocus/drag.png" /></div>
                    </div>
                    <div class="item-info">
                      <p>条码：{{ element.BarCode || '' }}</p>
                      <div @click.stop="handleMoveTop(index)" class="top-up">
                        <img src="../../assets/images/goodsFocus/top-up.png" />
                      </div>
                    </div>
                  </div>
                </div>
                <div :class="['selected-badge', { visible: element.selected }]">
                  <img src="@/assets/images/goodsFocus/selected.png" alt="Description" />
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </van-popup>
    <van-popup
      v-model:show="showCopyBarcode"
      round
      class="copy-barcode"
      :style="{ margin: '10% auto 10% auto', width: '85%', height: '25%' }"
    >
      <van-row>
        <van-col class="title" span="24">提示</van-col>
      </van-row>
      <van-row>
        <van-col class="content" span="24" style="">
          <p>系统暂未添加该商品</p>
        </van-col>
      </van-row>
      <van-row>
        <van-col class="content" span="24" style="">
          <p>商品条码：{{ scanCodeResult }}</p>
        </van-col>
      </van-row>
      <van-row gutter="10" class="btn-box">
        <van-col class="content" span="8" style="">
          <span @click="handleSearchIndustryData">查看行业数据</span>
        </van-col>
        <van-col class="content" span="8" style="">
          <span @click="copyToClipboard">
            <img src="../../assets/images/goodsFocus/copy.png" alt="" />
            复制条码
          </span>
        </van-col>
        <van-col class="content" span="8" style="">
          <span @click="showCopyBarcode = false">关闭</span>
        </van-col>
      </van-row>
    </van-popup>
  </div>
</template>

<style scoped lang="less">
.product-follow {
  height: 100%;
  background: #f2f3f7;
  .top-filter {
    padding: 43px 38px 23px 38px;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    .time-box {
      width: 256px;
      height: 69px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: #f2f3f7;
      border-radius: 23px;
      padding: 0 23px;
      .top-filter-date-icon {
        width: 31px;
        height: 31px;
        margin: 0 8px 0 0;
      }
      .top-filter-date-text {
        font-weight: 500;
        font-size: 24px;
        color: #333333;
        font-style: normal;
        text-transform: none;
      }
    }
    .top-filter-input {
      //  width: 50%;
      :deep(.el-input__wrapper) {
        width: 392px;
        height: 69px;
        background: #f2f3f7;
        border-radius: 23px;
        box-shadow: none;
      }
      :deep(.el-input__inner::placeholder) {
        color: #9ca3af;
      }
    }
  }
  .tabs-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;
    padding: 15px;
    overflow: hidden;

    .tabs-box-item-group {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      overflow: hidden;
      overflow-x: auto;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch; /* 提升移动端滚动体验 */
      scrollbar-width: none; /* 隐藏滚动条 Firefox */
      &::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 Chrome/Safari */
      }
      .tabs-box-items {
        // width: 168px;
        // height: 77px;
        padding: 21px 31px;
        font-size: 27px;
        color: #374151;
        font-style: normal;
        text-transform: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 44px 0 0;
        img {
          width: 31px;
          height: 31px;
        }
      }
      .tabs-box-items:last-child {
        margin: 0;
      }
      .tabs-box-items-activation {
        color: #fff;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        box-shadow: 0 8px 23px rgba(99, 102, 241, 0.3);
        border-radius: 50px;
      }
    }
  }
}

.data-overview {
  margin: 6px 0 8px 0;
  padding: 15px 15px 54px 15px;
  background: #fff;
  .overview-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .grid-content {
      width: 231px;
      height: 231px;
      border-radius: 23px;
      text-align: center;
      padding: 25px 25px 15px 25px;
      color: #fff;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .title {
        font-size: 27px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 0 0 8px 0;
        img {
          margin: 0 15px 0 0;
        }
        .saleValue-img {
          width: 31px;
          height: 31px;
        }
        .saleQty-img {
          width: 35px;
          height: 35px;
        }
        .stockQty-img {
          width: 35px;
          height: 32px;
        }
      }
      h2 {
        font-weight: 400;
        font-size: 46px;
        color: #ffffff;
        margin: 0 0 10px 0;
      }
      .year-on-year,
      .monopoly {
        font-size: 23px;
        color: #86efac;
        margin: 0 0 6px 0;
      }
      .monopoly {
        margin: 0;
      }
    }
    .grid-content:nth-child(1) {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      box-shadow: 0 8px 23px rgba(99, 102, 241, 0.3);
    }
    .grid-content:nth-child(2) {
      background: linear-gradient(90deg, #3498db 0%, #45aaf2 100%);
      box-shadow:
        0px 8 12px -8px rgba(0, 0, 0, 0.1),
        0px 19 29px -6px rgba(0, 0, 0, 0.1),
        0px 0 0px 0px rgba(0, 0, 0, 0),
        0px 0 0px 0px rgba(0, 0, 0, 0);
    }
    .grid-content:nth-child(3) {
      background: linear-gradient(90deg, #9b59b6 0%, #a55eea 100%);
      box-shadow:
        0px 8 12px -8px rgba(0, 0, 0, 0.1),
        0px 19 29px -6px rgba(0, 0, 0, 0.1),
        0px 0 0px 0px rgba(0, 0, 0, 0),
        0px 0 0px 0px rgba(0, 0, 0, 0);
      .year-on-year,
      .monopoly {
        color: #fff;
      }
    }
  }
}
.product-list {
  padding: 0 0 169px 0;
  background-color: #f2f3f7;
}
.product-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 23px 38px;
  margin: 0 0 23px 0;
  background: #fff;
  .left {
    font-size: 27px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #4b5563;
    .left-item-text {
      margin: 0 19px 0 0;
    }
    .left-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin: 0 29px 0 0;
      color: #6c5ce7;
      .icon {
        width: 31px;
        height: 31px;
        margin: 0 8px 0 0;
      }
    }
  }

  .right {
    .right-item {
      width: 127px;
      height: 62px;
      background: #5b48d9;
      border-radius: 8px;
      font-weight: 500;
      font-size: 27px;
      color: #ffffff;
      line-height: 62px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}

.product-list {
  .product-item {
    padding: 23px 23px 15px 23px;
    margin: 0 31px 19px 31px;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    border-radius: 23px;
    .product-item-header {
      display: flex;
      margin: 0 0 10px 0;
      .product-image {
        width: 135px;
        height: 135px;
        border-radius: 15px;
        margin: 0 23px 0 0;
        img {
          width: 135px;
          height: 135px;
        }
      }
      .product-details {
        flex: 2;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        margin: 0 15px 0 0;
        overflow: hidden;
        h4 {
          margin: 10px 0 29px 0;
          display: flex;
          align-items: center;
          span {
            display: inline-block;
          }
          .tag {
            margin: 0 10px 0 0;
            :deep(.van-tag) {
              width: 42px;
              height: 38px;
              line-height: 38px;
              padding: 0;
              border-radius: 10px;
              text-align: center;
            }
          }
          .name {
            font-size: 30px;
            color: #000000;
            text-align: left;
            font-style: normal;
            text-transform: none;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .product-info {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          p {
            font-size: 23px;
            color: #6b7280;
            margin: 0 25px 0 0;
          }
        }
      }
    }
    .product-item-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .product-item-bottom-item {
        width: 154px;
        height: 85px;
        background: #f2f3f7;
        border-radius: 12px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        small {
          color: #6b7280;
          font-size: 23px;
          margin: 8px 0 0 0;
        }
        span {
          margin: 0 0 6px 0;
          font-size: 27px;
        }
      }
    }
  }
}
.footer-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 169px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  .footer-box-item {
    width: 115px;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0 auto;
    img {
      width: 115px;
      height: 115px;
    }
    span {
      text-align: center;
      color: #6c5ce7;
      font-size: 23px;
    }
  }
}

.filter-title {
  margin: 80px 20px 20px 20px;
}
.time-box-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 50px 55px 0 55px;
  span:nth-child(1) {
    font-size: 27px;
    color: #6b7280;
  }
  span:nth-child(2) {
    font-size: 31px;
  }
  span:nth-child(3) {
    font-size: 27px;
    color: #6c5ce7;
  }
}
.time-box-list {
  margin: 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  button {
    width: 157px;
    height: 69px;
    border: none;
    margin: 0 0 30px 0;
    font-size: 27px;
  }
  button:nth-child(5),
  button:nth-child(6) {
    width: 325px;
    height: 85px;
    line-height: 85px;
    text-align: left;
  }

  :deep(.van-button--primary) {
    background: linear-gradient(90deg, #6c5ce7 0%, #8a7bff 100%);
    border-radius: 15px;
  }
  :deep(.van-button--default) {
    background: #f2f3f7;
    border-radius: 15px;
  }
  :deep(.van-button__text) {
    img {
      width: 31px;
      height: 31px;
      margin: 0 15px 00;
    }
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.customize-tiem-box {
  margin: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.el-input) {
    width: 40%;
  }
}
.customize-tiem-button {
  margin: 60px 20px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  button {
    width: 35%;
  }
}
.group-manager-top-btn {
  width: 95%;
  margin: 1.67vw;
  height: 113px;
  position: fixed;
  bottom: 38px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  box-shadow: 0 9px 28px rgba(99, 102, 241, 0.3);
  border-radius: 19px;
  font-size: 38px;
  color: #ffffff;
  line-height: 56px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.group-manager-item-wrap {
  .item {
    border-bottom: solid 1px #eee;
    border-radius: 4px;
    padding: 28px 49px 30px 56px;
    text-align: left;
    // width: 95%;
    // margin-left: 1.67vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item-text {
      display: flex;
      align-items: center;
      img {
        width: 38px;
        height: 38px;
        margin: 0 0 0 12px;
      }
    }

    .item-btn {
      img {
        width: 47px;
        height: 47px;
        margin: 0 0 0 38px;
      }
    }
  }

  .item:hover {
    cursor: move;
  }

  .item + .item {
    margin-top: 10px;
  }
}
.group-manager-title-box {
  font-weight: 500;
  background: #ffffff;
  padding: 33px 38px 35px 38px;
  border-bottom: solid 1px #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.van-icon) {
    color: #6b7280;
    font-size: 47px;
  }
}
.group-manager-tip {
  font-weight: 400;
  font-size: 28px;
  color: #818181;
  line-height: 56px;
  text-align: left;
  font-style: normal;
  margin: 10px 0 0 26px;
}

// 商品管理
.goods-manager {
  .goods-manager-title-box {
    font-weight: 500;
    background: #ffffff;
    padding: 33px 38px 35px 38px;
    border-bottom: solid 1px #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    :deep(.van-icon) {
      color: #6b7280;
      font-size: 47px;
    }
  }
  .goods-manager-top {
    padding: 28px 43px 19px 26px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .goods-manager-top-radio {
      display: flex;
      :deep(.van-cell) {
        padding: 0;
      }
      :deep(.van-badge__wrapper) {
        border-radius: 50%;
        margin: 0 16px 0 0;
      }
      :deep(.van-checkbox__icon--checked .van-icon) {
        background-color: #5b48d9;
        border-color: #5b48d9;
      }
      .goods-manager-top-info {
        // margin-left: auto;
        // margin-right: 2.33vw;
      }
    }
    .goods-manager-btn {
      font-weight: 400;
      font-size: 30px;
      color: #ef4444;
    }
  }

  .item {
    padding: 26px 26px 26px 0;
    // margin: 0 2vw 2vw 2vw;
    // border-radius: 5px;
    // box-shadow: 0px 2px 20px rgba(153, 153, 153, 0.35);
    position: relative;
    display: flex;
    align-items: center;

    border-bottom: 1px solid #f3f4f6;
    .checkbox-wrapper {
      // position: absolute;
      // right: 10px;
      // top: 10px;
      // z-index: 2;
      // padding: 5px;

      margin: 0 26px;
      :deep(.van-badge__wrapper) {
        border-radius: 50%;
      }
      :deep(.van-checkbox__icon--checked .van-icon) {
        background-color: #5b48d9;
        border-color: #5b48d9;
      }
    }
    .goods-manager-right-box {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      overflow: hidden;
      .item-image {
        width: 107px;
        height: 107px;
        display: block;
        margin: 8px 26px 0 0;
        img {
          width: 107px;
          height: 107px;
          object-fit: cover; /* 或 contain，取决于您希望的效果 */
        }
      }
      .item-details {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
        // margin: 0 15px 0 0;

        h4 {
          margin: 0 0 9px 0;
          display: flex;
          align-items: center;
          span {
            display: inline-block;
          }
          .tag {
            // padding: 10px 15px;
            // background-color: #f2f2f2;
            // margin: 0 10px 0 0;
            margin: 0 10px 0 0;
            :deep(.van-tag) {
              width: 47px;
              height: 43px;
              line-height: 43px;
              padding: 0;
              border-radius: 10px;
              text-align: center;
            }
          }
          .name {
            width: 100%;
            font-size: 30px;
            white-space: nowrap; /* 禁止换行 */
            overflow: hidden; /* 超出部分隐藏 */
            text-overflow: ellipsis; /* 显示省略号 */
          }
        }
        .item-info {
          display: flex;
          align-items: center;
          justify-content: space-between;

          p {
            font-size: 26px;
            color: #6b7280;
          }
        }
        .drag {
          // width: 51px;
          // height: 51px;
          width: 43px;
          height: 39px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .top-up {
          width: 43px;
          height: 39px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .selected-badge {
      display: none;
      position: absolute;
      top: 0;
      right: 7.27vw;
      width: 0;
      height: 0;
    }
    .visible {
      display: block;
    }
  }
  .item-select {
    background: #f0eeff;
    border-bottom: none;
  }
}
.copy-barcode {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 5%;
  margin: 10%;
  width: 70%;
  height: 35%;
  --van-popup-round-radius: 10px;
  .title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 30px;
    align-items: center;
  }
  .content {
    display: flex;
    flex-direction: column;
    font-size: 26px;
    margin-bottom: 20px;
  }
  .btn-box {
    .content {
      display: flex;
      flex-direction: column;
      font-size: 26px;
      margin-bottom: 20px;
      text-align: center;
    }
    .content:nth-child(1) {
      color: #7255ff;
    }
    .content:nth-child(2) {
      span {
        display: flex;
        align-items: center;

        img {
          width: 24px;
          height: 24px;
          margin: 0 10px 0;
        }
      }
    }
    .content:last-child {
      color: #666666;
    }
  }
  .button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    font-weight: bold;
    color: #6b7280;
    margin-top: 50px;
    margin-bottom: 50px;
  }
}
</style>
