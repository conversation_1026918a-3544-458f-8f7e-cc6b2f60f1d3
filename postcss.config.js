export default {
  plugins: {
    'postcss-px-to-viewport-8-plugin': {
      unitToConvert: 'px', // 需要转换的单位，默认为 px
      viewportWidth: 750, // 设计稿的视口宽度，通常是 750px
      unitPrecision: 2, // 转换后保留的小数位数
      propList: ['*'], // 可以从 px 转换成 vw 的属性列表，* 代表所有
      viewportUnit: 'vw', // 需要转换成的视口单位
      fontViewportUnit: 'vw', // 字体单位转换成 vw
      selectorBlackList: ['.ignore', '.hairlines'], // 忽略转换的 CSS 类
      minPixelValue: 1, // 小于或等于 1px 的值不进行转换
      mediaQuery: false, // 是否允许在媒体查询中转换
      exclude: [/node_modules/], // 排除文件夹
    },
  },
}
