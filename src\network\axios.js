import axios from 'axios'
import config from '../config/config' // 导入 config.js 配置
import { showLoadingToast, closeToast, showToast } from 'vant'
import { version } from '../../package.json'
// 创建 Axios 实例
const instance = axios.create({
  baseURL: config.baseURL, // 你的 API 基础 URL
  timeout: 50000, // 设置请求超时
})

// 请求计数器
let requestCount = 0

// 存储所有请求的取消控制器
const cancelControllers = new Set()

// 构建通用请求参数
const buildCommonRequestParams = (data) => {
  return {
    format: 'JSON',
    param: data,
    sign: '',
    source: 'COZE',
    timestamp: new Date().getTime(),
    version: version,
  }
}

// 取消所有请求并重置状态
export const cancelAllRequests = () => {
  // 取消所有未完成的请求
  cancelControllers.forEach((controller) => {
    controller.abort()
  })
  cancelControllers.clear()

  // 重置请求计数
  requestCount = 0

  // 关闭加载提示
  closeToast()
}

// 统一的请求配置处理函数
const handleRequestConfig = (config) => {
  try {
    // 确保 config 和 headers 存在
    if (!config) {
      throw new Error('请求配置不能为空')
    }

    if (!config.headers) {
      config.headers = {}
    }

    // 添加取消控制器
    const controller = new AbortController()
    config.signal = controller.signal
    cancelControllers.add(controller)

    // 添加 token 到请求头
    const token = sessionStorage.getItem('token')
    if (token) {
      config.headers['Content-Type'] = 'application/json;charset=UTF-8'
      config.headers['nexora-token'] = token
      config.headers['angela-m-token'] = token
    }

    // 构建并添加通用请求参数
    config.data = buildCommonRequestParams(config.data)

    return config
  } catch (error) {
    console.error('请求配置处理失败:', error)
    throw error
  }
}

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    try {
      // 如果是第一个请求，显示加载提示
      if (requestCount === 0) {
        showLoadingToast({
          message: '加载中...',
          forbidClick: true,
          duration: 0,
        })
      }
      requestCount++

      // 处理请求配置
      return handleRequestConfig(config)
    } catch (error) {
      // 请求配置处理出错时，确保关闭loading
      requestCount--
      if (requestCount === 0) {
        closeToast()
      }
      showToast({
        type: 'fail',
        message: '请求配置错误',
        duration: 3000,
      })
      return Promise.reject(error)
    }
  },
  (error) => {
    // 请求发送失败时，确保关闭loading
    requestCount--
    if (requestCount === 0) {
      closeToast()
    }
    showToast({
      type: 'fail',
      message: '请求发送失败',
      duration: 3000,
    })
    return Promise.reject(error)
  },
)

// 统一的错误处理函数
const handleError = (error, defaultMessage = '请求失败') => {
  const errorMessage = error?.response?.data?.message || error?.message || defaultMessage
  showToast({
    type: 'fail',
    message: errorMessage,
    duration: 3000,
    onClose: () => {
      requestCount--
      if (requestCount === 0) {
        closeToast()
      }
    },
  })
  return Promise.reject(error)
}

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    try {
      // 请求完成后移除取消控制器
      if (response.config.signal) {
        cancelControllers.delete(response.config.signal.controller)
      }

      // 首先检查 HTTP 状态码
      if (response.status !== 200) {
        return handleError(response, '服务器响应异常')
      }

      // 处理 blob 类型响应
      if (response.config.responseType === 'blob') {
        // 检查 blob 响应是否有效
        if (response.data instanceof Blob) {
          closeToast()
          return response
        }
        closeToast()
        return handleError(response, '文件下载失败')
      }

      // 处理普通 JSON 响应
      if (response.data.code === 0) {
        return response.data
      }
      return handleError(response.data, response.data.message)
    } finally {
      // 成功响应时立即减少计数
      if (response?.data?.code === 0) {
        requestCount--
        if (requestCount === 0) {
          closeToast()
        }
      }
    }
  },
  (error) => {
    // 如果是取消请求的错误，不显示错误提示
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }

    try {
      // 请求完成后移除取消控制器
      if (error.config?.signal) {
        cancelControllers.delete(error.config.signal.controller)
      }

      // 响应错误处理
      if (error.response) {
        // 根据不同的 HTTP 状态码处理
        if (error.response.status === 401) {
          // 401: 未授权，跳转到登录页
          return handleError(error, '未授权，请重新登录')
        }
        // 其他错误处理
        return handleError(error)
      }
      // 网络错误或其他错误
      return handleError(error, '网络错误，请稍后重试')
    } finally {
      // 错误处理中的计数减少已经在 handleError 的 onClose 回调中处理
    }
  },
)

export default instance
