import axios from '@/network/axios' // 导入配置好的 axios 实例


// 获取企业体检数据
export const getShopCheckData = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/shopCheck/get/data',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
