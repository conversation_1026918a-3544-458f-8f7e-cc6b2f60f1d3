import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/report/standardReport',
      name: 'standardReport',
      component: () => import('../views/report/standardReport.vue'),
    },
    {
      path: '/report/sheetReport',
      name: 'sheetReport',
      component: () => import('../views/report/sheetReport.vue'),
    },
    {
      path: '/report/customizeReport',
      name: 'customizeReport',
      component: () => import('../views/report/customizeReport.vue'),
    },
    {
      path: '/ai',
      name: 'ai',
      component: () => import('../views/ai/ai.vue'),
    },
    {
      path: '/industryData',
      name: 'industryData',
      component: () => import('../views/industryData/industryData.vue'),
    },
    {
      path: '/goodsDetails',
      name: 'goodsDetails',
      component: () => import('../views/goodsDetails/goodsDetails.vue'),
    },
    {
      path: '/goodsInfo',
      name: 'goodsInfo',
      component: () => import('../views/goodsInfo/goodsInfo.vue'),
    },
    {
      path: '/goodsFocus',
      name: 'goodsFocus',
      component: () => import('../views/goodsFocus/index.vue'),
    },
    {
      path: '/goodsFocus/shareUser',
      name: 'shareUser',
      component: () => import('../views/goodsFocus/shareUser.vue'),
    },
    {
      path: '/goodsFocus/searchGoods',
      name: 'searchGoods',
      component: () => import('../views/goodsFocus/searchGoods.vue'),
    },
    {
      path: '/shopCheck',
      name: 'shopCheck',
      component: () => import('../views/shopCheck/index.vue'),
    },
  ],
})

export default router
