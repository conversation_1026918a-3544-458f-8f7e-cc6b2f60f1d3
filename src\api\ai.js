import _requestNoPromptLoading from '../network/ai'
let _FUNCTION_NAME = 'saas-operations-api/'
let _API_NAME = ''
export const api = {
  nexoraAuthLogin: _nexoraAuthLogin,
  conversationList: _conversationList,
  conversationDetail: _conversationDetail,
  newConversation: _newConversation,
  deleteConversation: _deleteConversation,
  getWxJsapiSignature: _getWxJsapiSignature
}

// 移除接口定义
// interface MessageInfo {
//   id: string;
//   conversationId: string,
//   botId: string,
//   chatId: string,
//   role: string,
//   content: string,
//   contentType: string,
//   createdAt: string,
//   updatedAt: string,
//   type: string
// }

// export interface ConversationInfo {
//   id: string;
//   userId: string;
//   userName: string;
//   userMobile: string;
//   orgId: string;
//   corpNo: string;
//   corpName: string;
//   userType: number;
//   angelaUserId: string;
//   cozeConversationId: string;
//   messagesInfo: Array<MessageInfo>;
//   title: string;
//   createUser: string;
//   createUserId: string;
//   lastModifyUser: string;
//   lastModifyUserId: string;
//   lastModifyTime: string;
//   createTime: string;
//   lastMessage?: string;
// }

function _nexoraAuthLogin(data) {
  let url = _FUNCTION_NAME + 'agentapi/nexora/auth/login'
  return _requestNoPromptLoading({
    url: url,
    method: 'post',
    data,
  })
}

/* POST  /saas-operations-api/agentapi/nexora/chat/conversation/list 会话列表 */
function _conversationList(data) {
  let url = _FUNCTION_NAME + 'agentapi/nexora/chat/conversation/list' //'saasapi/nexora/chat/conversation/list'
  return _requestNoPromptLoading({
    url: url,
    method: 'post',
    data,
  })
}

/* GET  /saas-operations-api/agentapi/nexora/chat/conversation/{id} 会话详情 */
function _conversationDetail(id) {
  let url = _FUNCTION_NAME + 'agentapi/nexora/chat/conversation/' + id //'saasapi/nexora/chat/conversation/' + id
  return _requestNoPromptLoading({
    url: url,
    method: 'get',
  })
}

/* POST  /saas-operations-api/saasapi/nexora/chat/conversation 创建新会话 */
function _newConversation() {
  let url = _FUNCTION_NAME + 'agentapi/nexora/chat/conversation' //'saasapi/nexora/chat/conversation'
  return _requestNoPromptLoading({
    url: url,
    method: 'post',
    data: {},
  })
}

/* GET  /saas-operations-api/agentapi/nexora/chat/conversation/delete/{id} 删除会话 */
function _deleteConversation(id) {
  let url = _FUNCTION_NAME + 'agentapi/nexora/chat/conversation/delete/' + id
  return _requestNoPromptLoading({
    url: url,
    method: 'get',
  })
}

// /* POST  /saas-operations-api/saasapi/nexora/chat/stream AI会话分页查看 */
// function _newConversation() {
//   let url = _FUNCTION_NAME + 'saasapi/nexora/chat/stream'
//   return _requestNoPromptLoading({
//     url: url,
//     method: 'post',
//     data: params,
//   })
// }

/* POST  /saas-operations-api/agentapi/nexora/chat/getWxJsapiSignature 获取微信jssdk的WxJsapiSignature */
function _getWxJsapiSignature(data) {
  let url = _FUNCTION_NAME + 'agentapi/nexora/chat/getWxJsapiSignature'
  return _requestNoPromptLoading({
    url: url,
    method: 'post',
    data: data,
  })
}
