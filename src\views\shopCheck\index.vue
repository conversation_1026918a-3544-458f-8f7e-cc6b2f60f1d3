<script setup>
import { ref, onBeforeMount, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { getShopTree } from '@/api/reportApi'
import { createRadarChart, } from '@/echarts/echarts_Js/echartsJs'
import echartsDemo from '@/components/echarts/echartsDemo.vue'
import { getShopCheckData } from '@/api/shopCheck'
import config from '../../config/config'
import { api } from '@/api/ai'
import wx from 'weixin-webview-jssdk'
import { showToast } from 'vant'


const route = useRoute()
const loginStatus = ref(false)
const shopsTreeRef = ref(null)
//门店列表
const shopsTreeData = ref([])
const showShopsTree = ref(false)
// 树形控件展示（必须，但不展示）
const treeValue = ref()
// 树控件选中的值
const treeCheckedArr = ref([])
//树形控件默认参数
const defaultProps = ref({
  children: 'children',
  label: 'name',
})
const currentShop = ref({
    name: '全部门店',
    shopId: '0',
  })
const radarChartData = ref({})
const radarEchartsRef = ref(null)
// 接口数据
const apiData = ref({})
// 战力数据
const currentRate = ref(100)
const pointData = ref({
  rank: 0,
  totalRank: 0,
  rankChange: 0,
  pointValue: 0,
})
// 雷达图数据
const radarData = ref({
  indicators: [
    { name: '销售额达成率', max: 100 },
    { name: '客单价', max: 100 },
    { name: '客件数', max: 100 },
    { name: '异常商品指数', max: 100 },
    { name: '毛利率', max: 100 },
    { name: '周转天数', max: 100 },
  ],
  series: [
    { name: '当前门店', value: [0, 0, 0, 0, 0, 0] },
  ],
});
// 指标数据
const metricData = ref([
  {
    title: '销售额达成率',
    rank: 32,
    rankChange: 3,
    value: '80%',
    medianValue: '68%',
  },
])
// 建议数据
const activeKey = ref('')
const aiMsgContent = ref('')
const suggestData = ref([
  {
    tag: 'warn',
    title: '客流量下降',
    content: '客流量较上月下降20.3%，建议优化门店外部引流策略，增加社交媒体曝光度。',
  },
  {
    tag: 'pass',
    title: '客单价提升',
    content: '客单价同比提升15.2%，建议继续优化高价值商品的陈列位置，提升交叉销售。',
  },
  {
    tag: 'info',
    title: '库存优化',
    content: '当前库存周转率为3.2次/月，建议对滞销品进行促销清理，提高周转效率。',
  },
])

//获取门店Tree数据
const getShopData = async () => {
  let data = {
    head: {
      accessToken: sessionStorage.getItem('token'),
    },
  }
  const res = await getShopTree(data)
  shopsTreeData.value = res
  treeCheckedArr.value.push(res[0].children[0].children[1].shopId)
  currentShop.value = res[0].children[0].children[1]
  nextTick(() => {
    shopsTreeRef.value.setCheckedKeys(treeCheckedArr.value, true)
  })
}
// 点击门店树
const treeCheckChange = (currentNode, hasCheck) => {
  treeCheckedArr.value.length = 0
  if (hasCheck) {
    treeCheckedArr.value.push(currentNode.shopId)
    shopsTreeRef.value.setCheckedKeys(treeCheckedArr.value, true)
    currentShop.value = currentNode
  } else {
    shopsTreeRef.value.setChecked(currentNode, hasCheck, false)
  }
  console.log(currentShop.value)
}
// 获取建议
const getSuggestData = async () => {
  aiMsgContent.value = ''
  api
    .newConversation({bizType: 2})
    .then(async (res) => {
      activeKey.value = res.result.id
      suggestData.value.length = 0
      let path = `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/stream`
      try {
        const response = await fetch(
          path, // `${config.aiBaseURL}saas-operations-api/agentapi/nexora/chat/stream`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache',
              'angela-a-token': sessionStorage.getItem('angela-a-token'),
              'vdf-source': 'vdf-front',
            },
            body: JSON.stringify({
              prompt: JSON.stringify(apiData.value),
              localConversationId: activeKey.value,
              localChatId: '',
              cozeBotId: '',
              cozeConversationId: '',
              cozeChatId: '',
              actionCode: 'SHOP_CHECK',
              ossFiles: [],
              customPromptId: '',
            }),
          },
        )
        // promptsItemskey.value = ''
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const reader = response.body?.getReader()
        if (!reader) throw new Error('No reader available')
        const decoder = new TextDecoder()

        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          const chunkStr = decoder.decode(value)
          const lines = chunkStr.split('\n').filter(Boolean)

          for (const line of lines) {
            const sseString = line.startsWith('data:') ? line.slice('data:'.length) : line
            if (sseString.length < 1) continue
            let sseEvent
            try {
              sseEvent = JSON.parse(sseString)
            } catch (err) {
              console.error('Error parsing SSE line:', err, line)
              continue
            }

            const apiRetCode = sseEvent.code
            if (apiRetCode != 0) {
              // 关闭读流
              await reader.cancel()
              break
            } else {
              if (sseEvent.result?.cardMsg === '思考结束') {
                // 清空之前发的正在思考
                aiMsgContent.value = ''
              }
              // 更新当前对话的coze会话id，botId，消息内容
              aiMsgContent.value += sseEvent.result?.message
            }
          }
        }
        console.log(aiMsgContent.value, 'aiMsgContent.value')
        suggestData.value = JSON.parse(aiMsgContent.value)
      } catch (error) {
        console.error('Error sending message:', error)
      }
    })
    .catch((error) => {
      console.log(error)
    })

}
// 拉取数据
const getData = () => {
  getShopCheckData({shopId: currentShop.value.shopId}).then((res) => {
    apiData.value = res
    if (apiData.value == null || apiData.value === 'null') {
      metricData.value.length = 0
      suggestData.value.length = 0
      radarData.value = {
        indicators: [
          { name: '销售额达成率', max: 100 },
          { name: '周转天数', max: 100 },
          { name: '毛利率', max: 100 },
          { name: '异常商品指数', max: 100 },
          { name: '客件数', max: 100 },
          { name: '客单价', max: 100 },
        ],
        series: [
          { name: '当前门店', value: [0, 0, 0, 0, 0, 0] },
        ],
      }
      // 更新数据
      let chartConfig = createRadarChart(radarData.value)
      if (chartConfig) {
        radarChartData.value = chartConfig.chart
      }
      return
    }
    // 处理数据
    pointData.value.rank = res.totalScoreRank || 0
    pointData.value.totalRank = res.totalScoreRankDen || 0
    pointData.value.rankChange = res.totalScoreRank - res.prevTotalScoreRank || 0
    pointData.value.pointValue = res.totalScore || 0

    radarData.value.indicators.length = 0
    radarData.value.series.length = 0
    radarData.value.indicators.push(
      { name: '销售额达成率', max: res.maxSalevalueFinishrateRank < 0 ? 100 : res.maxSalevalueFinishrateRank },
      { name: '周转天数', max: res.maxDsiRank < 0 ? 100 : res.maxDsiRank },
      { name: '毛利率', max: res.maxProfitRateRank < 0 ? 100 : res.maxProfitRateRank },
      { name: '异常商品指数', max: res.maxAbyskuRateRank < 0 ? 100 : res.maxAbyskuRateRank },
      { name: '客件数', max: res.maxAvgNumPurchasesRank < 0 ? 100 : res.maxAvgNumPurchasesRank },
      { name: '客单价', max: res.maxAvgCustomerPriceRank < 0 ? 100 : res.maxAvgCustomerPriceRank },
    )
    radarData.value.series.push(
      { name: '当前门店', value: [
        res.salevalueFinishrateRank > 0 ? res.maxSalevalueFinishrateRank - res.salevalueFinishrateRank + 1 : 0,
        res.dsiRank > 0 ? res.maxDsiRank - res.dsiRank + 1 : 0,
        res.profitRateRank > 0 ? res.maxProfitRateRank - res.profitRateRank + 1 : 0,
        res.abyskuRateRank > 0 ? res.maxAbyskuRateRank - res.abyskuRateRank + 1 : 0,
        res.avgNumPurchasesRank > 0 ? res.maxAvgNumPurchasesRank - res.avgNumPurchasesRank + 1 : 0,
        res.avgCustomerPriceRank > 0 ? res.maxAvgCustomerPriceRank - res.avgCustomerPriceRank + 1 : 0] },
    )
    metricData.value.length = 0
    metricData.value.push(
      {
        title: '销售额达成率',
        rank: res.salevalueFinishrateRank > 0 ? res.salevalueFinishrateRank : '/',
        rankChange: res.salevalueFinishrateRank > 0 ? res.prevSalevalueFinishrateRank - res.salevalueFinishrateRank : 0,
        value: res.salevalueFinishrateStr == "null" ? 0 : res.salevalueFinishrateStr,
        medianValue: res.medianSalevalueFinishrateStr,
      },
      {
        title: '周转天数',
        rank: res.dsiRank > 0 ? res.dsiRank : '/',
        rankChange: res.dsiRank > 0 ? res.prevDsiRank - res.dsiRank : 0,
        value: res.dsiStr == "null" ? 0 : res.dsiStr,
        medianValue: res.medianDsiStr,
      },
      {
        title: '毛利率',
        rank: res.profitRateRank > 0 ? res.profitRateRank : '/',
        rankChange: res.profitRateRank > 0 ? res.prevProfitRateRank - res.profitRateRank : 0,
        value: res.profitRateStr == "null" ? 0 : res.profitRateStr,
        medianValue: res.medianProfitRateStr,
      },
      {
        title: '异常商品指数',
        rank: res.abyskuRateRank > 0 ? res.abyskuRateRank : '/',
        rankChange: res.abyskuRateRank > 0 ? res.prevAbyskuRateRank - res.abyskuRateRank : 0,
        value: res.abyskuRateStr == "null" ? 0 : res.abyskuRateStr,
        medianValue: res.medianAbyskuRateStr,
      },
      {
        title: '客件数',
        rank: res.avgNumPurchasesRank > 0 ? res.avgNumPurchasesRank : '/',
        rankChange: res.avgNumPurchasesRank > 0 ? res.prevAvgNumPurchasesRank - res.avgNumPurchasesRank : 0,
        value: res.avgNumPurchasesStr == "null" ? 0 : res.avgNumPurchasesStr,
        medianValue: res.medianAvgNumPurchasesStr,
      },
      {
        title: '客单价',
        rank: res.avgCustomerPriceRank > 0 ? res.avgCustomerPriceRank : '/',
        rankChange: res.avgCustomerPriceRank > 0 ? res.prevAvgCustomerPriceRank - res.avgCustomerPriceRank : 0,
        value: res.avgCustomerPriceStr == "null" ? 0 : res.avgCustomerPriceStr,
        medianValue: res.medianAvgCustomerPriceStr,
      },
    )

    // 更新数据
    let chartConfig = createRadarChart(radarData.value)
    if (chartConfig) {
      radarChartData.value = chartConfig.chart
    }

    // 获取建议
    getSuggestData()
  })
}
// 切换门店确定
const confirm = () => {
  showShopsTree.value = false
  getData()
}

const nexoraAuthLogin = () => {
  api
    .nexoraAuthLogin()
    .then(async (res) => {
      if (res.code * 1 === 0) {
        sessionStorage.setItem('userInfo', JSON.stringify(res.result))
        loginStatus.value = true
        await getShopData()
        getData()
      } else {
        showToast({
          type: 'fail',
          message: 'res.message',
          duration: 3000,
        })
        loginStatus.value = false
        wx.miniProgram.redirectTo({ url: '../login/login' })
      }
    })
    .catch((error) => {
      console.log(error)
    })
}

onMounted(() => {
  nexoraAuthLogin()
})
onBeforeMount(() => {
  // 组件挂载前执行，此时模板已编译但尚未挂载到DOM
  document.title = '门店体检'
  if (JSON.stringify(route.query) !== '{}') {
    if (
      typeof route.query.t !== 'undefined' &&
      typeof route.query.u !== 'undefined'
    ) {
      sessionStorage.setItem('token', route.query.t)
      localStorage.setItem('setToken', route.query.t)
      sessionStorage.setItem('userId', route.query.u)
    }
  }
})
</script>

<template>
  <div class="container">

    <div class="shop-selector">
      <div class="shop-selector-title"><van-icon name="shop-o" />{{ currentShop.name.split('#')[1] || currentShop.name }}</div>
      <div class="shop-selector-switch" @click="showShopsTree = !showShopsTree">
        切换门店
        <van-icon name="arrow" />
      </div>
    </div>

    <div class="point-card">
      <div class="title">
        <div class="text">门店战力值</div>
      </div>
      <div class="content">
        <div class="circle">
          <van-circle
            v-model:current-rate="currentRate"
            :rate="100"
            :speed="100"
            :stroke-width="80"
            color="#5B42D9"
            layer-color="#5B42D9"
            :text="pointData.pointValue"
          />
        </div>
        <div class="text">
          <van-row class="header">
            <van-col span="6" class="title-text">排名</van-col>
            <van-col span="8" class="rank-text">{{ pointData.rank }}/{{ pointData.totalRank }}</van-col>
            <van-col span="10" class="compare-text">
              <sapn v-if="pointData.rankChange > 0"><van-icon style="transform: scaleY(-1);" name="down" />较昨日 +{{ pointData.rankChange }}</sapn>
              <sapn v-if="pointData.rankChange < 0"><van-icon name="down" />较昨日 {{ pointData.rankChange }}</sapn>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="24" class="desc-text">战胜了{{ ((pointData.totalRank-pointData.rank)/pointData.totalRank * 100).toFixed(2) }}%的门店</van-col>
          </van-row>
        </div>
      </div>
    </div>

    <div class="radar-card">
      <div class="title">
        <div class="text">战力雷达图</div>
      </div>
      <div class="content">
        <echartsDemo :height='`100vw`' :echartsData="radarChartData" :ref="radarEchartsRef"></echartsDemo>
      </div>
    </div>

    <div class="item-title">
      指标详情
    </div>
    <div class="metric-card" v-if="metricData.length === 0">
      <van-row class="metric-header">
        <van-col span="15" class="title-text"><span class="title-tag"></span><span>暂无数据</span></van-col>
        <van-col span="9" class="rank-text"></van-col>
      </van-row>
    </div>
    <div class="metric-card" v-for="(item,index) in metricData" :key="index">
      <van-row class="metric-header">
        <van-col span="14" class="title-text"><span class="title-tag"></span><span>{{ item.title}}</span></van-col>
        <van-col span="10" class="rank-text">
          排名：{{ item.rank }}&nbsp;
          <sapn v-if="item.rankChange > 0" class="rank-up"><van-icon style="transform: scaleY(-1);" name="down" />+{{ item.rankChange }}</sapn>
          <sapn v-if="item.rankChange < 0" class="rank-down"><van-icon name="down" />{{ item.rankChange }}</sapn>
        </van-col>
      </van-row>
      <van-row class="metric-content">
        <van-col span="24">
          <van-row>
            <van-col span="20" class="title-text">{{ item.title }}</van-col>
            <van-col span="4" class="value-text">{{ item.value }}</van-col>
          </van-row>
          <van-row>
            <van-col span="20" class="title-text">企业中位数</van-col>
            <van-col span="4" class="value-text">{{ item.medianValue }}</van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>

    <div class="suggest-card">
      <van-row class="suggest-header">
        <van-col span="24" class="title-text"><span class="title-tag">AI</span>灵犀建议</van-col>
      </van-row>
      <van-row class="suggest-content" v-if="metricData.length === 0">
        <van-col span="24">
          <van-row>
            <van-col span="2" class="tag">
              <van-icon name="warn-o" size="20" color="#F59E0B" />
            </van-col>
            <van-col span="22">
              <van-row>
                <van-col span="24" class="title-text">暂无数据</van-col>
              </van-row>
              <van-row>
                <van-col span="24" class="value-text"></van-col>
              </van-row>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row class="suggest-content" v-if="suggestData.length === 0 && metricData.length !== 0">
        <van-col span="24">
          <van-row>
            <van-col span="24">
              <van-loading size="48"/>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
      <van-row class="suggest-content" v-for="(item,index) in suggestData" :key="index">
        <van-col span="24">
          <van-row>
            <van-col span="2" class="tag">
              <van-icon v-if="item.tag == 'warn'" name="warn-o" size="20" color="#F59E0B" />
              <van-icon v-if="item.tag == 'pass'" name="passed" size="20" color="#22C55E" />
              <van-icon v-if="item.tag == 'info'" name="info-o" size="20" color="#3B82F6" />
            </van-col>
            <van-col span="22">
              <van-row>
                <van-col span="24" class="title-text">{{ item.title }}</van-col>
              </van-row>
              <van-row>
                <van-col span="24" class="value-text">{{ item.content }}</van-col>
              </van-row>
            </van-col>
          </van-row>
        </van-col>
      </van-row>
    </div>

    <van-popup v-model:show="showShopsTree" position="bottom" :style="{ height: '100%' }">
      <div class="van-popup-box">
        <div class="operate-box">
          <span @click="showShopsTree = false">取消</span>
          <span>选择门店</span>
          <span @click="confirm">确定</span>
        </div>
        <div class="van-popup-tree-box">
          <el-tree ref="shopsTreeRef" v-if="shopsTreeData.length > 0" v-model="treeValue" :data="shopsTreeData"
            default-expand-all show-checkbox style="width: 98%" node-key="shopId"
            :default-checked-keys="treeCheckedArr" :props="defaultProps" @check-change="treeCheckChange" :check-strictly="true" />
        </div>
      </div>
    </van-popup>
  </div>


</template>

<style scoped lang="less">
.container {
  margin: 0;
  padding: 10px;
  background-color: #000;

  .shop-selector {
    margin: 1.33vw;
    margin-bottom: 5vw;
    padding: 15px 20px;
    border-radius: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: #252525;

    .shop-selector-title {
      font-size: 32px;
      color: #fff;
      margin-right: 1.33vw;

      i {
        font-size: 5.4vw;
        color: #5b42d9;
        margin-right: 10px;
      }
    }

    .shop-selector-switch {
      margin-left: auto;
      color: #5b42d9;
    }
  }
  .metric-card {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .metric-header {
      margin-bottom: 24px;
      margin-left: 16px;
      margin-right: 16px;
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #fff;
      .title-text {
        display: flex;
        align-items: center;
        font-size: 32px;
        .title-tag {
          width: 8px;
          height: 70px;
          border-radius: 20%;
          background-color: #5B42D9;
          margin-right: 20px;
          padding: 4px;
        }
      }
      .rank-text {
        font-size: 40px;
        display: flex;
        align-items: center;
        .rank-up {
          font-size: 28px;
          color: #22C55E;
        }
        .rank-down {
          font-size: 28px;
          color: #EF4444;
        }
      }
    }
    .metric-content {
      background-color: #2A2A2A;
      border-radius: 8px;
      margin-left: 16px;
      margin-right: 16px;
      margin-bottom: 16px;
      padding-top: 10px;
      padding-bottom: 10px;
      .title-text {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 28px;
        color: #D1D5DB;
        margin-top: 10px;
        margin-left: 22px;
        margin-bottom: 10px;
      }
      .value-text {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 32px;
        color: #fff;
        margin-top: 10px;
        margin-right: 22px;
        margin-bottom: 10px;
      }
    }
  }
  .suggest-card {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .suggest-header {
      margin-bottom: 20px;
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #fff;
      .title-text {
        display: flex;
        font-size: 36px;
        .title-tag {
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          border-radius: 50%;
          background-color: #5B42D9;
          color: #fff;
          font-size: 24px;
          margin-right: 20px;
          padding: 10px;
        }
      }
    }
    .suggest-content {
      background-color: #2A2A2A;
      border-radius: 8px;
      margin-left: 16px;
      margin-right: 16px;
      margin-bottom: 24px;
      margin-top: 24px;
      .tag {
        margin-top: 30px;
      }
      .title-text {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 28px;
        color: #fff;
        margin-top: 26px;
        margin-left: 4px;
        margin-right: 22px;
        margin-bottom: 4px;
      }
      .value-text {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 24px;
        color: #D1D5DB;
        margin-top: 4px;
        margin-left: 4px;
        margin-right: 22px;
        margin-bottom: 24px;
      }
    }
  }
  .item-title {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    margin: 0 10px;
    margin-bottom: 32px;
    font-size: 36px;
    color: #fff;
  }
  .radar-card {
    display: flex-column;
    justify-content: flex-start;
    align-items: center;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .title {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 30px;
      .text {
        font-size: 32px;
        color: #fff;
      }
    }
    .content {
      justify-content: flex-start;
    }
  }
  .point-card {
    display: flex-column;
    background: linear-gradient(to right, #2A2A2A, #1A1A1A);
    margin: 0 10px;
    margin-bottom: 5vw;
    padding: 20px;
    border-radius: 12px;
    .title {
      margin-bottom: 30px;
      .text {
        font-size: 32px;
        color: #fff;
      }
    }
    .content {
      display: flex;
      flex-direction: row;
      .circle {
        --van-circle-text-color: #fff;
        --van-circle-text-font-size: 48px;
        --van-circle-text-font-weight: normal;
      }
      .text {
        display: flex-column;
        align-items: center;
        background-color: #2A2A2A;
        border-radius: 8px;
        margin-top: 30px;
        margin-left: 20px;
        margin-bottom: 30px;
        padding: 20px;
        width: 100%;
        .header {
          display: flex-column;
          align-items: center;
          .title-text {
            justify-content: flex-start;
            font-size: 32px;
            color: #9CA3AF;
          }
          .rank-text {
            font-size: 36px;
            color: #fff;
          }
          .compare-text {
            align-items: center;
            font-size: 28px;
            color: #5B42D9;
            .rank-up {
              font-size: 28px;
              color: #22C55E;
            }
            .rank-down {
              font-size: 28px;
              color: #EF4444;
            }
          }
        }
        .desc-text {
          font-size: 28px;
          color: #fff;
          margin-top: 30px;
        }
      }
    }
  }
}
.van-popup-box {
    width: 100%;
    height: 98%;
    padding: 10px;
    overflow: hidden;
  }
.operate-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    span:first-child {
      color: #969799;
    }
    span:nth-child(2) {
      color: black;
      font-weight: bold;
    }
    span:last-child {
      color: #1989fa;
    }
  }
.van-popup-tree-box {
  margin: 15px 30px;
  padding: 0 0 100px 0;
  background: #fff;
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
}
</style>
