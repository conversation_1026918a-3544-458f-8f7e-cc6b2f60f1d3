<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
import { searchGoodsDetails } from '@/api/goodsDetails'

const barcode = ref('')

const itemInfo = reactive({
  imgUrl: 'https://example.com/path/to/crispy-shark-chocolate.jpg', // 更新为实际的商品图片URL
  //   itemName: '雀巢（Nestle）脆脆鲨休闲零食涂层威化饼干 办公室早餐儿童点心 巧克力味446.4g',
  //   brand: '雀巢',
  //   barCode: '1151155112424222',
  //   specification: '10g*24',
})

const gethGoodsDetails = async () => {
  let data = {
    searchCondition: barcode.value,
  }
  console.log(data, 'data')

  try {
    const res = await searchGoodsDetails(data)
    console.log(res, 'res-gethGoodsDetails')
    Object.assign(itemInfo, res.productList[0])
    console.log(itemInfo, 'itemInfo')
    nextTick(() => {
      document.title = itemInfo.productCname
    })
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  document.title = '商品标库'
  console.log(route.query, 'HomeView route.query')
  if (route.query.t && route.query.b) {
    sessionStorage.setItem('token', route.query.t)
    barcode.value = route.query.b
    gethGoodsDetails()
  } else {
    console.log('跳转携带参数缺失')
  }
})
</script>

<template>
  <div class="goods-details">
    <!-- 商品图片 -->
    <van-image
      width="100%"
      height="300px"
      :src="itemInfo.mainHttpUrl || 'https://placehold.co/600x400'"
      alt="商品图片"
    />

    <!-- 商品标题 -->
    <div class="product-title">
      {{ itemInfo.productCname }}
    </div>

    <!-- 商品属性 -->
    <div class="product-attributes">
      <div class="attribute-item">
        <span>商品属性</span>
      </div>
    </div>

    <div class="product-attributes-child">
      <div class="attribute-item">
        <span>品牌</span>
        <span>{{ itemInfo.brandName }}</span>
      </div>
      <div class="attribute-item">
        <span>条码</span>
        <span>{{ itemInfo.upcCode }}</span>
      </div>
      <div class="attribute-item">
        <span>规格</span>
        <span>{{ itemInfo.spec }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.goods-details {
  //   padding: 20px;
  background-color: #f6f7f8;

  .product-title {
    font-size: 30px; // 调整字体大小以匹配设计
    padding: 25px 30px;
    font-weight: bold;
    background-color: #fff;
    margin-bottom: 20px; // 增加标题与属性之间的间距
  }

  .product-attributes {
    padding: 0px 30px;
    margin-top: 20px;
    background-color: #fff; // 添加背景色以区分区域
    .attribute-item {
      display: flex;
      justify-content: space-between;
      padding: 30px 0;
    }
  }
  .product-attributes-child {
    margin-top: 20px;
    background-color: #fff;
    .attribute-item {
      display: flex;
      justify-content: flex-start;
      padding: 30px 0 30px 70px;

      &:nth-child(even) {
        background-color: #f5f5f8; // 添加偶数行背景色
      }
      span:first-child {
        color: #666;
        width: 30%;
      }
    }
  }
}
</style>
