// 过滤标准报表表头
export const filterStandardTheHeader = (data, that) => {
  let arr = []
  arr.push({
    title: data.a_2,
    key: 'a_2',
    render(h, params) {
      return h('div', [h('span', params.row.a_2)])
    },
  })
  let dataList = Object.keys(data).sort()
  dataList.forEach((i) => {
    for (let j = 1; j <= 20; j++) {
      if (i == 'd_' + j) {
        arr.push({
          title: data[i],
          key: i,
          render(h, params) {
            return h('div', params.row[i])
          },
        })
      }
    }
  })

  return arr
}

export const filterSheetTheHeader = (data, that, lockCol) => {
  let tableList = []
  tableList.push({
    title: data[0].a_2,
    align: 'center',
    key: 'a_2',
    sortable: 'custom',
    render(h, params) {
      return h('div', [h('span', params.row.a_2)])
    },
  })
  for (let i = 0; i <= data[0].z_3; i++) {
    let index = i + 1
    for (let j in data[0]) {
      if ('d_' + index == j) {
        tableList.push({
          title: data[0][j],
          align: 'center',
          key: j,
          // minWidth: 105,
          sortable: 'custom',
          render(h, params) {
            return h('div', params.row[j])
          },
        })
      }
    }
  }
  return tableList
}

/**
 * 下载文件流
 * data  文件流
 * name  文件名称
 */
export const downLoad = (data, name) => {
  const url = window.URL.createObjectURL(new Blob([data]))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', name)
  document.body.appendChild(link)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}
