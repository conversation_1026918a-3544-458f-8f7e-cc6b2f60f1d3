<!-- 
  折线图组件
  使用 ECharts 实现的可复用的折线图组件
  支持自定义样式、大小和图表配置
-->
<template>
  <div class="lineEcharts">
    <div
      :class="className"
      :id="id"
      :style="{ height: height, width: width }"
      ref="myEcharts"
    ></div>
  </div>
</template>

<script setup>
// 导入必要的依赖
import * as echarts from 'echarts'
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from 'vue' // nextTick

// 定义组件属性
const props = defineProps({
  // 自定义类名
  className: {
    type: String,
    default: 'yourClassName',
  },
  // 图表容器ID
  id: {
    type: String,
    default: 'yourID',
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%',
  },
  // 图表高度
  height: {
    type: String,
    default: '250px',
  },
  // ECharts配置项
  echartsData: {
    type: Object,
  },
})

// 图表DOM引用
const myEcharts = ref(null)
// 图表实例
let chartExample = ref(null)
// 本地响应式配置
const localConfig = ref({})

/**
 * 初始化图表
 * 创建ECharts实例并设置配置项
 */
const initEcharts = () => {
  chartExample.value = markRaw(echarts.init(myEcharts.value))
  chartExample.value.clear()
  localConfig.value = { ...props.echartsData }
  chartExample.value.setOption(localConfig.value)
  chartExample.value.resize()
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartExample.value) {
    // 根据屏幕宽度调整图表配置
    const isMobile = window.innerWidth < 768
    const option = chartExample.value.getOption()

    // 调整标签显示
    if (option.series) {
      option.series.forEach((series) => {
        if (series.label) {
          series.label.fontSize = isMobile ? 10 : 12
          series.label.position = isMobile ? 'inside' : 'top'
        }
      })
    }

    // 调整图表布局
    if (option.grid) {
      option.grid.left = isMobile ? '5%' : '3%'
      option.grid.right = isMobile ? '5%' : '4%'
      option.grid.bottom = isMobile ? '15%' : '3%'
    }

    chartExample.value.setOption(option)
    chartExample.value.resize()
  }
}

// 监听配置项变化，实时更新图表
watch(
  () => props.echartsData,
  (newValue) => {
    if (chartExample.value && newValue) {
      // 确保在下一个 tick 更新图表
      nextTick(() => {
        chartExample.value.setOption(newValue, true)
        chartExample.value.resize()
      })
    }
  },
  {
    deep: true,
    immediate: true,
  },
)
const emit = defineEmits(['update:legendSelected'])
// 图表 Legend 点击事件
const handleLegendSelectChanged = (params) => {
  console.log(params, 'params')
  emit('update:legendSelected', {
    label: params.label,
    selected: !props.echartsData.legend.selected[params.label],
  })
  // props.echartsData.legend.selected[params.label] = !props.echartsData.legend.selected[params.label]
  // // 更新图表显示
  // this.chart.setOption(this.echarts_obj, true)
}
defineExpose({ handleLegendSelectChanged })
// 组件挂载后初始化图表
onMounted(() => {
  if (props.echartsData) {
    console.log(props.echartsData, 'props.echartsData')
    nextTick(() => {
      initEcharts()
    })
  }
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 组件卸载前清理图表实例
onBeforeUnmount(() => {
  if (chartExample.value) {
    chartExample.value.dispose()
    chartExample.value = null
  }
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.lineEcharts {
  width: 100%;
  overflow: hidden;
}
</style>
