import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useReportStore = defineStore('report', () => {
  const reportConfigurationData = ref(null)
  const reportConfigurationType = ref(null)

  function setReportConfigurationData(data) {
    reportConfigurationData.value = data
    // Also store in sessionStorage
    sessionStorage.setItem('reportConfigurationData', JSON.stringify(data))
  }

  function setReportConfigurationType(type) {
    reportConfigurationType.value = type
    sessionStorage.setItem('reportConfigurationType', type)
  }

  return {
    reportConfigurationData,
    reportConfigurationType,
    setReportConfigurationData,
    setReportConfigurationType,
  }
})
