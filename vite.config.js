import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// 获取当前时间戳
const timestamp = new Date().getTime()

// https://vite.dev/config/
export default defineConfig({
  base: '/nexora-front/',
  plugins: [
    vue(),
    // vueDevTools()
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  build: {
    outDir: 'nexora-front',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        entryFileNames: `js/[name]-[hash]-${timestamp}.js`,
        chunkFileNames: `js/[name]-[hash]-${timestamp}.js`,
        assetFileNames: ({ name }) => {
          if (/\.(gif|jpe?g|png|svg|webp)$/.test(name)) {
            return `images/[name]-[hash]-${timestamp}.[ext]`
          }
          if (/\.css$/.test(name)) {
            return `css/[name]-[hash]-${timestamp}.[ext]`
          }
          return `assets/[name]-[hash]-${timestamp}.[ext]`
        },
      },
    },
  },

  server: {
    host: '0.0.0.0', // 本地地址
    port: 3000, // 本地端口号
    proxy: {
      '/nexora-front/saas-operations-api': {
        target: 'http://localhost:20221',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/nexora-front/, ''),
      },
      '/saas-operations-api': {
        target: 'http://localhost:20221',
        changeOrigin: true,
      },
      // '/nexora-front': {
      //   target: 'http://localhost:20221',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/nexora-front/, '')
      // }
    },
    cors: true, // 开启跨域支持
  },
})
