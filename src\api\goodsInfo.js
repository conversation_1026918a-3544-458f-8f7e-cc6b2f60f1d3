import axios from '@/network/axios' // 导入配置好的 axios 实例

// http://192.168.31.128:20201/saas-basic-api/nexora/api/r/api/itemall_product/search

// 获取指标
export const getGoodsTargets = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/getGoodsTargets',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

export const getGoodsFields = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/rpt/getGoodsFields',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
