<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
// import { Microphone } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog, showSuccessToast, showFailToast, showToast } from 'vant'
import { getGoodsList, getIndustryData } from '@/api/goodsFocus'
import defaultImg from '@/assets/images/goodsFocus/default.png'
import { api } from '@/api/ai'
import wx from 'weixin-webview-jssdk'
import { Microphone } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const currentGroupId = ref('')
const searchHistory = ref([])
const searchName = ref('')
const goodsList = ref([])
const industryList = ref([])
const showGoodsList = ref(false)
const activeTab = ref(0)
// 点击录音按钮
const nextRecording = ref(false)
// 按住录音按钮 开始true，停止false
const audioLoading = ref(false)
// 录音本地id
const recordLocalId = ref('')

// 加载搜索历史
const loadSearchHistory = () => {
  // 从本地存储中获取搜索历史
  const history = localStorage.getItem('searchHistory')
  if (history) {
    searchHistory.value = JSON.parse(history)
  } else {
    searchHistory.value = []
  }
}

// 添加搜索历史
const addSearchHistory = (name) => {
  if (!name) return
  // 先检查是否已经存在
  if (searchHistory.value.includes(name)) {
    // 删除原来的
    searchHistory.value.splice(searchHistory.value.indexOf(name), 1)
  }
  // 添加搜索历史
  searchHistory.value.unshift(name)
  // 保存到本地存储
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

// 清除搜索历史
const clearHistory = () => {
  searchHistory.value = []
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

// 搜索商品
const searchGoods = async (name) => {
  if (name) {
    searchName.value = name
  }

  if (searchName.value == '') return

  // 添加搜索历史
  addSearchHistory(searchName.value)

  // 获取商品列表
  const res = await getGoodsList({
    type: '1',
    goods: searchName.value,
  })
  goodsList.value = res
  showGoodsList.value = true

  // goodsList.value = [
  //       {
  //           "goodsId": "102001",
  //           "goodsName": "可口可乐碳酸饮料桃子味限量版500ml 规格:500ml/瓶",
  //           "barCode": "4902102127141",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/540904/3/73-540904-2615012415761426.png!std150",
  //           "isSplitGoods": 0,
  //           "num": 1
  //       },
  //       {
  //           "goodsId": "102017",
  //           "goodsName": "可口可乐水动乐缤纷莓果味饮料600mlT 规格:600ml/瓶",
  //           "barCode": "6975682480508",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/995096/3/73-995096-0917393272205402.jpg!std150",
  //           "isSplitGoods": 0,
  //           "num": 2
  //       },
  //       {
  //           "goodsId": "102910",
  //           "goodsName": "可口可乐摩登罐330ml 规格:330ml/听",
  //           "barCode": "6928804014570",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/408508/3/73-408508-2118592182144338.jpg!std150",
  //           "isSplitGoods": 0,
  //           "num": 3
  //       },
  //       {
  //           "goodsId": "121553",
  //           "goodsName": "可口可乐2L+雪碧2L组合装 规格:2L*2/组",
  //           "barCode": "6928804011784",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/647868/3/73-647868-2820391347580865.JPG!std150",
  //           "isSplitGoods": 0,
  //           "num": 19
  //       }
  //   ]
}

// 搜索行业数据
const searchIndustry = async () => {
  if (searchName.value == '') return

  if (!/^\d{8,}$/.test(searchName.value)) {
    showToast('请输入不少于8位数字的条码')
    return
  }

  // 添加搜索历史
  addSearchHistory(searchName.value)

  // 获取行业数据
  const res = await getIndustryData({
    orgId: '123', // 固定值
    barcode: searchName.value,
  })
  industryList.value = res.itemList
  showGoodsList.value = true

  // industryList.value = [
  //       {
  //           "goodsId": "102001",
  //           "goodsName": "可口可乐碳酸饮料桃子味限量版500ml 规格:500ml/瓶",
  //           "barCode": "4902102127141",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/540904/3/73-540904-2615012415761426.png!std150",
  //           "isSplitGoods": 0,
  //           "num": 1
  //       },
  //       {
  //           "goodsId": "102017",
  //           "goodsName": "可口可乐水动乐缤纷莓果味饮料600mlT 规格:600ml/瓶",
  //           "barCode": "6975682480508",
  //           "goodsImg": "https://itemall-std-img.viwor.net/images/73/995096/3/73-995096-0917393272205402.jpg!std150",
  //           "isSplitGoods": 0,
  //           "num": 2
  //       },
  //   ]
}

const clearSearch = () => {
  searchName.value = ''
  showGoodsList.value = false
}

const wxConfig = () => {
  api
    .getWxJsapiSignature(location.href.split('#')[0])
    .then((res) => {
      const result = res.result
      console.log(JSON.stringify(res))
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: result.appId, // 必填，公众号的唯一标识
        timestamp: result.timestamp, // 必填，生成签名的时间戳
        nonceStr: result.nonceStr, // 必填，生成签名的随机串
        signature: result.signature, // 必填，签名
        jsApiList: ['startRecord', 'stopRecord', 'translateVoice'], // 必填，需要使用的JS接口列表
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

const startAction = () => {
  showToast('开始录音')
  audioLoading.value = true
  wx.startRecord()
}
const endAction = () => {
  nextRecording.value = false
  showToast('停止录音')
  audioLoading.value = false
  wx.stopRecord({
    success: function (res) {
      console.log(JSON.stringify(res))
      recordLocalId.value = res.localId
      wx.translateVoice({
        localId: res.localId, // 需要识别的音频的本地Id，由录音相关接口获得
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: function (res) {
          console.log('识别结果：', res)
          searchName.value = res.translateResult
        },
        fail: function (res) {
          console.log('识别失败：', JSON.stringify(res))
        },
      })
    },
    fail: function (res) {
      console.log('停止录音失败：', JSON.stringify(res))
    },
  })
}
// 语音输入
const audioInput = () => {
  nextRecording.value = !nextRecording.value
  audioLoading.value = !audioLoading.value
  // TODO：调起语音识别
  if (nextRecording.value) {
    wx.startRecord()
  } else {
    wx.stopRecord({
      success: function (res) {
        console.log(JSON.stringify(res))
        recordLocalId.value = res.localId
        wx.translateVoice({
          localId: res.localId, // 需要识别的音频的本地Id，由录音相关接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function (res) {
            console.log('识别结果：', res)
            searchName.value = res.translateResult
          },
          fail: function (res) {
            console.log('识别失败：', JSON.stringify(res))
          },
        })
      },
      fail: function (res) {
        console.log('停止录音失败：', JSON.stringify(res))
      },
    })
  }
  // 调用搜索
  searchGoods(searchName.value)
}

// tab切换
const onClickTab = (tab) => {
  console.log(tab.name, 'tab.name')
  activeTab.value = tab.name
  if (tab.name == 0) {
    searchGoods()
  } else {
    searchIndustry()
  }
}

// tab切换前检查
const beforeChange = (index) => {
  // 返回 false 表示阻止此次切换
  // 校验searchName.value的值是不少于8位数字的全条码
  if (index === 1 && !/^\d{8,}$/.test(searchName.value)) {
    console.log(activeTab.value)
    showToast('请输入不少于8位数字的全条码')
    return false
  }
  return true
}

// 进入商品详情
const handleGoodsClick = (item) => {
  console.log(activeTab.value, 'activeTab.value')
  if (activeTab.value * 1 === 0) {
    router.push({
      path: '/goodsInfo',
      query: {
        t: sessionStorage.getItem('token'),
        u: sessionStorage.getItem('userId'),
        g: item.goodsId,
      },
    })
    return
  }
  if (activeTab.value * 1 === 1) {
    console.log(item, 'item')
    console.log(
      JSON.parse(sessionStorage.getItem('userInfo')).orgId,
      'sessionStorage.getItem(userId)',
    )

    router.push({
      path: '/industryData',
      query: {
        t: sessionStorage.getItem('token'),
        o: JSON.parse(sessionStorage.getItem('userInfo')).orgId,
        b: item.barCode,
      },
    })
  }
}

onMounted(() => {
  // document.title = '抄送人'
  wxConfig()
  if (JSON.stringify(route.query) !== '{}') {
    if (typeof route.query.t !== 'undefined' && typeof route.query.u !== 'undefined') {
      sessionStorage.setItem('token', route.query.t)
      sessionStorage.setItem('userId', route.query.u)

      if (route.query.g) {
        currentGroupId.value = route.query.g
        // 用户搜索历史
        loadSearchHistory()
      } else if (route.query.b) {
        searchName.value = route.query.b
        showGoodsList.value = true
        activeTab.value = 1
        searchIndustry()
      }
    }
  }
})
onBeforeUnmount(() => {
  goodsList.value = []
  industryList.value = []
})
</script>

<template>
  <div class="search">
    <div class="top-filter">
      <div class="search-container">
        <van-search
          v-model="searchName"
          show-action
          placeholder="搜索商品名称/条码"
          @search="searchGoods('')"
          @clear="clearSearch"
        >
          <template #right-icon>
            <el-button
              v-if="nextRecording"
              type="primary"
              color="#7B4EFF"
              @mousedown="startAction"
              @mouseup="endAction"
              @mouseleave="endAction"
              @touchstart="startAction"
              @touchend="endAction"
              >{{ audioLoading ? '松开结束' : '按住说话' }}</el-button
            >
            <el-button
              v-if="!nextRecording"
              plain
              @click="nextRecording = !nextRecording"
              color="#7B4EFF"
            >
              <el-icon>
                <Microphone size="20" />
              </el-icon>
            </el-button>
          </template>
          <template #action>
            <!-- <van-button @click="searchGoods('')" type="primary" size="small">搜索</van-button> -->
            <el-button type="primary" @click="searchGoods('')">搜索</el-button>
          </template>
        </van-search>
      </div>
    </div>
    <div class="history-box" v-if="!showGoodsList">
      <div class="title">
        <div class="title-text">历史搜索</div>
        <div class="title-icon" @click="clearHistory">
          <van-icon name="delete-o" />
        </div>
      </div>
      <div class="history-list">
        <div
          class="item"
          v-for="(item, index) in searchHistory"
          :key="index"
          @click="searchGoods(item)"
        >
          <div class="item-text">{{ item }}</div>
        </div>
      </div>
    </div>
    <div class="goods-list" v-if="showGoodsList">
      <van-tabs v-model:active="activeTab" @click-tab="onClickTab" :before-change="beforeChange">
        <van-tab title="企业内数据">
          <div
            class="item"
            v-for="item in goodsList"
            :key="item.goodsId"
            @click="handleGoodsClick(item)"
          >
            <div class="item-image">
              <img :src="item.goodsImg || defaultImg" alt="Product Image" style="width: 100%" />
            </div>
            <div class="item-details">
              <p>
                <span class="name">{{ item.goodsName }}</span>
              </p>
              <div class="item-info">
                <p>商品条码：{{ item.barCode }}</p>
                <p>商品编码：{{ item.goodsId }}</p>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab title="相关行业数据">
          <div
            class="item"
            v-for="item in industryList"
            :key="item.barCode"
            @click="handleGoodsClick(item)"
          >
            <div class="item-image">
              <img :src="item.itemImgUrl || defaultImg" alt="Product Image" />
            </div>
            <div class="item-details">
              <p>
                <span class="name">{{ item.itemName }}</span>
              </p>
              <div class="item-info">
                <p>商品条码：{{ item.barCode }}</p>
                <p>商品编码：{{ item.goodsId || '' }}</p>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<style scoped lang="less">
.search {
  background: #f2f3f7;
  :deep(.van-search__content) {
    border-radius: 23px;
  }
  .search-container {
    :deep(.van-field__right-icon) {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    :deep(.el-button) {
      // padding: 0 15px;
      border: none;
      background: none;
    }
    :deep(.el-icon) {
      color: #7255ff;
    }
    :deep(.el-button--primary) {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      // box-shadow: 0px 9px 26px 0px rgba(99, 102, 241, 0.3);
    }
  }
  .top-filter {
  }

  .history-box {
    background-color: #fff;

    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .title-text {
        font-size: 3.87vw;
        padding: 2.33vw;
      }

      .title-icon {
        margin-left: auto;
        padding: 2.33vw;
        font-size: 4.87vw;
      }
    }

    .history-list {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      margin: 0 1.67vw;

      .item {
        display: inline-block;
        // width: 154px;
        // height: 62px;
        padding: 12px 23px;
        background: #f3f4f6;
        border-radius: 19229px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10px 10px 0;
        font-size: 27px;
        color: #333;
        font-weight: 500;
      }
    }
  }

  .goods-list {
    margin: 27px 0 0 0;
    padding: 0 0 31px 0;
    .item {
      display: flex;
      align-items: flex-start;
      background: #ffffff;
      // background: red;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      border-radius: 23px;
      margin: 27px 31px;
      padding: 23px;

      .item-image {
        width: 135px;
        height: 135px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 23px 0 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .item-details {
        height: 135px;
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // padding: 10px 0;

        p {
          // margin: 0 0 15px 0;
          span {
            display: inline-block;
            width: 100%;
            font-size: 31px;
            color: #333333;
            line-height: 46px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            white-space: nowrap; /* 禁止换行 */
            overflow: hidden; /* 超出部分隐藏 */
            text-overflow: ellipsis; /* 显示省略号 */
          }
        }

        .item-info {
          p {
            font-size: 23px;
            color: #6b7280;
            // line-height: 31px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }
    }
  }
}
</style>
