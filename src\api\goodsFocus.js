import axios from '@/network/axios' // 导入配置好的 axios 实例

// 首页数据
export const selectGoodFocusIndex = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/selectGoodFocusIndex',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 根据商品编号查询用户组合列表
export const selectUserGoodsGroupList = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/selectUserGoodsGroupList',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 添加分组
export const addGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/insertGoodsGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 编辑分组
export const updateGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/updateGoodsGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 删除分组
export const deleteGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/deleteGoodsGroupById',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 分组排序
export const sortGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/sortGoodsGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 抄送分享组合
export const shareGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/shareGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 删除商品
export const deleteGroupGoods = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/deleteFocusGoodsFromGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 排序商品
export const sortGroupGoods = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/saveGroupGoodsSort',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 搜索商品
export const getGoodsList = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/selectGoodsByIdOrName',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 查询行业数据
export const getIndustryData = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/searchBarcodeList',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 添加商品
export const insertGoodsFocus = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/insertGoodsFocus',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 删除商品
export const deleteFocusGoodsFromGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/deleteFocusGoodsFromGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}

// 删除分享分组
export const deleteShareGroup = async (data) => {
  try {
    const response = await axios.post('nexora/api/r/call', data, {
      headers: {
        'nexora-r-path': '/NexoraRAPI/angela/api/goodsFocus/deleteShareGroup',
      },
    })
    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
