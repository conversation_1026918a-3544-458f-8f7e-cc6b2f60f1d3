import axios from '@/network/axios' // 导入配置好的 axios 实例

export const barcodeList = async (data) => {
  try {
    const response = await axios.post(
      'nexora/api/r/industry/data/price/compare/search/barcode/list',
      data,

      {},
      // {
      //   headers: {
      //     'nexora-r-path': '/NexoraRAPI/angela/api/rpt/menu/all',
      //   },
      // },
    )

    return response.result
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
