import './assets/css/main.css'

import Vant from 'vant'
import 'vant/lib/index.css'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// import Vconsole from 'vconsole'
// let vConsole = new Vconsole()
// vConsole.hideSwitch() // 初始化后立即隐藏浮标
// window.vConsole = vConsole // 挂载到全局，方便App.vue调用

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.use(Vant)

app.use(ElementPlus)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// app.use(vConsole)

//版本号
app.directive('show-version', {
  mounted(element) {
    const handler = (event) => {
      if (event.ctrlKey && event.altKey && event.key === 'v') {
        element.style.display = 'block'
      } else {
        element.style.display = 'none'
      }
    }
    document.addEventListener('keydown', handler)
    element._keydownHandler = handler // 保存事件处理函数的引用
  },
  unmounted(element) {
    document.removeEventListener('keydown', element._keydownHandler)
    delete element._keydownHandler
  },
})

// 检查并卸载已有实例
const container = document.querySelector('#app')
if (container.__vue_app__) {
  container.__vue_app__.unmount()
}

app.mount('#app')

export default app
