<script setup>
import { ref, onMounted } from 'vue'
import { RouterView } from 'vue-router'
import packageJson from '../package.json'

const packageJsonVersion = ref()
const showVersion = ref(false)
const clickCount = ref(0)

const handleClick = () => {
  clickCount.value++
  // if (clickCount.value > 10 && window.vConsole) {
  //   window.vConsole.showSwitch() // 显示vConsole浮标
  // }
}

onMounted(() => {
  packageJsonVersion.value = packageJson.version
  showVersion.value = false
  console.log('packageJsonVersion', packageJsonVersion.value)
})
</script>

<template>
  <div @click="handleClick" style="height: 100vh">
    <RouterView />
    <div
      v-show-version
      style="
        display: none;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 9999;
        width: 100px;
        height: 15px;
        font-size: 14px;
        color: black;
      "
    >
      <div>ver.{{ packageJsonVersion }}</div>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
